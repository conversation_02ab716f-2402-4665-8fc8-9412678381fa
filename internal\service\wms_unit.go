package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/jinzhu/copier"
)

type WmsUnitService interface {
	Create(ctx context.Context, req *v1.WmsUnitCreateParams) error
	Update(ctx context.Context, id uint, req *v1.WmsUnitUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.WmsUnitResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewWmsUnitService(
	service *Service,
	wmsUnitRepository repository.WmsUnitRepository,
) WmsUnitService {
	return &wmsUnitService{
		Service:           service,
		wmsUnitRepository: wmsUnitRepository,
	}
}

type wmsUnitService struct {
	*Service
	wmsUnitRepository repository.WmsUnitRepository
}

// 物料单位相关方法实现
func (s *wmsUnitService) Create(ctx context.Context, req *v1.WmsUnitCreateParams) error {
	unit := &model.WmsUnit{}
	if err := copier.Copy(unit, req); err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 租户ID
	unit.TenantId = user.TenantId

	// 创建人
	if user.Nickname != "" {
		unit.CreatedBy = user.Nickname
	} else {
		unit.CreatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsUnitRepository.Create(ctx, unit); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsUnitService) Update(ctx context.Context, id uint, req *v1.WmsUnitUpdateParams) error {
	unit, err := s.wmsUnitRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if unit.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	if err := copier.Copy(unit, req); err != nil {
		return err
	}

	// 更新人
	if user.Nickname != "" {
		unit.UpdatedBy = user.Nickname
	} else {
		unit.UpdatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsUnitRepository.Update(ctx, unit); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsUnitService) Delete(ctx context.Context, id uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 是否是该租户数据
	unit, err := s.wmsUnitRepository.Get(ctx, id)
	if err != nil {
		return err
	}
	if unit.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsUnitRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsUnitService) BatchDelete(ctx context.Context, ids []uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 获取目标记录
	units, err := s.wmsUnitRepository.GetByIds(ctx, ids)
	if err != nil {
		return err
	}
	
	// 过滤非本租户数据
	var newIds []uint
	for _, unit := range units {
		if unit.TenantId == user.TenantId {
			newIds = append(newIds, unit.ID)
		}
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsUnitRepository.BatchDelete(ctx, newIds); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsUnitService) Get(ctx context.Context, id uint) (*v1.WmsUnitResponse, error) {
	unit, err := s.wmsUnitRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if unit.TenantId != user.TenantId {
		return nil, v1.ErrForbidden
	}

	response := &v1.WmsUnitResponse{}
	if err := copier.Copy(response, unit); err != nil {
		return nil, err
	}

	response.CreatedAt = unit.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = unit.UpdatedAt.Format(time.RFC3339)

	return response, nil
}

func (s *wmsUnitService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	params.AddFilter("tenant_id", user.TenantId)

	units, total, err := s.wmsUnitRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.WmsUnitResponse, 0, len(units))
	for _, unit := range units {
		response := &v1.WmsUnitResponse{}
		if err := copier.Copy(response, unit); err != nil {
			return nil, err
		}

		response.CreatedAt = unit.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = unit.UpdatedAt.Format(time.RFC3339)

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
