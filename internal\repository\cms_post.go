package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type CmsPostRepository interface {
	Create(ctx context.Context, post *model.CmsPost) error
	Update(ctx context.Context, post *model.CmsPost) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.CmsPost, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.CmsPost, int64, error)
}

func NewCmsPostRepository(
	repository *Repository,
) CmsPostRepository {
	return &cmsPostRepository{
		Repository: repository,
	}
}

type cmsPostRepository struct {
	*Repository
}

func (r *cmsPostRepository) Create(ctx context.Context, post *model.CmsPost) error {
	if err := r.DB(ctx).Create(post).Error; err != nil {
		return err
	}
	return nil
}

func (r *cmsPostRepository) Update(ctx context.Context, post *model.CmsPost) error {
	if err := r.DB(ctx).Save(post).Error; err != nil {
		return err
	}
	return nil
}

func (r *cmsPostRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.CmsPost{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *cmsPostRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.CmsPost{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *cmsPostRepository) Get(ctx context.Context, id uint) (*model.CmsPost, error) {
	var post model.CmsPost
	if err := r.DB(ctx).First(&post, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &post, nil
}

func (r *cmsPostRepository) List(ctx context.Context, params *pagination.Params) ([]*model.CmsPost, int64, error) {
	var records []*model.CmsPost
	var total int64

	db := r.DB(ctx).Model(&model.CmsPost{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.CmsPost{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
