package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

type WmsStockHandler struct {
	*Handler
	wmsStockService service.WmsStockService
}

func NewWmsStockHandler(
	handler *Handler,
	wmsStockService service.WmsStockService,
) *WmsStockHandler {
	return &WmsStockHandler{
		Handler:         handler,
		wmsStockService: wmsStockService,
	}
}

// Create godoc
// @Summary 创建库存记录
// @Schemes
// @Description 创建新的库存记录
// @Tags 仓储模块,库存管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.WmsStockCreateParams true "库存信息"
// @Success 200 {object} v1.Response
// @Router /wms/stocks [post]
func (h *WmsStockHandler) Create(ctx *gin.Context) {
	var req v1.WmsStockCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsStockService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新库存记录
// @Schemes
// @Description 更新指定ID的库存记录信息
// @Tags 仓储模块,库存管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "库存记录ID"
// @Param request body v1.WmsStockUpdateParams true "库存信息"
// @Success 200 {object} v1.Response
// @Router /wms/stocks/{id} [patch]
func (h *WmsStockHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.WmsStockUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsStockService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除库存记录
// @Schemes
// @Description 删除指定ID的库存记录
// @Tags 仓储模块,库存管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "库存记录ID"
// @Success 200 {object} v1.Response
// @Router /wms/stocks/{id} [delete]
func (h *WmsStockHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsStockService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除库存记录
// @Schemes
// @Description 批量删除指定IDs的库存记录
// @Tags 仓储模块,库存管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "库存记录IDs"
// @Success 200 {object} v1.Response
// @Router /wms/stocks [delete]
func (h *WmsStockHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsStockService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取库存记录
// @Schemes
// @Description 获取指定ID的库存记录信息
// @Tags 仓储模块,库存管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "库存记录ID"
// @Param _expand query string false "展开关联信息，支持: item,sku" example:"item,sku"
// @Success 200 {object} v1.Response{data=v1.WmsStockResponse}
// @Router /wms/stocks/{id} [get]
func (h *WmsStockHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	stock, err := h.wmsStockService.Get(ctx, uint(id), strings.Split(ctx.Query("_expand"), ","))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, stock)
}

// List godoc
// @Summary 获取库存记录列表
// @Schemes
// @Description 分页获取库存记录列表
// @Tags 仓储模块,库存管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param itemId query int false "物料ID筛选" example:"1"
// @Param skuId query int false "物料规格ID筛选" example:"1"
// @Param areaId query string false "库位ID筛选" example:"1"
// @Param _expand query string false "展开关联信息，支持: item,sku" example:"item,sku"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.WmsStockResponse}}
// @Router /wms/stocks [get]
func (h *WmsStockHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	// 物料ID筛选
	if itemId := ctx.DefaultQuery("itemId", ""); itemId != "" {
		params.AddFilter("item_id", itemId)
	}

	// SKU ID筛选
	if skuId := ctx.DefaultQuery("skuId", ""); skuId != "" {
		params.AddFilter("sku_id", skuId)
	}

	// 库位筛选
	if areaId := ctx.DefaultQuery("areaId", ""); areaId != "" {
		params.AddFilter("area_path_any", areaId)
	}

	result, err := h.wmsStockService.List(ctx, &params, strings.Split(ctx.Query("_expand"), ","))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
