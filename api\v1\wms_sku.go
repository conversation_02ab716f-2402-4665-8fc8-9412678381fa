package v1

import "gorm.io/datatypes"

type WmsSkuCreateParams struct {
	Name      string         `json:"name" binding:"required,max=255" example:"iPhone 15 128GB 黑色"`
	Code      string         `json:"code" binding:"required,max=64" example:"IP15-128-BLK"`
	Unit      string         `json:"unit" binding:"required,max=64" example:"个"`
	Min       float64        `json:"min" example:"10.5"`
	Attrs     datatypes.JSON `json:"attrs" swaggertype:"object" example:"{\"color\":\"黑色\",\"storage\":\"128GB\",\"network\":\"5G\"}"`
	Summary   string         `json:"summary" example:"苹果手机 iPhone 15 128GB 黑色版本"`
	Order     int            `json:"order" example:"1"`
	Status    bool           `json:"status" example:"true"`
	ItemId    uint           `json:"itemId" binding:"required" example:"1"`
	TenantId  uint           `json:"tenantId" example:"1"`
	CreatedBy string         `json:"createdBy" example:"管理员"`
	UpdatedBy string         `json:"updatedBy" example:"管理员"`
}

type WmsSkuUpdateParams struct {
	WmsSkuCreateParams
}

type WmsSkuResponse struct {
	ID        uint           `json:"id"`
	Name      string         `json:"name"`
	Code      string         `json:"code"`
	Unit      string         `json:"unit"`
	Min       float64        `json:"min"`
	Attrs     datatypes.JSON `json:"attrs"`
	Summary   string         `json:"summary"`
	Order     int            `json:"order"`
	Status    bool           `json:"status"`
	ItemId    uint           `json:"itemId"`
	TenantId  uint           `json:"tenantId"`
	CreatedBy string         `json:"createdBy"`
	UpdatedBy string         `json:"updatedBy"`
	CreatedAt string         `json:"createdAt"`
	UpdatedAt string         `json:"updatedAt"`
}
