package middleware

import (
	"bytes"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/jwt"
	"daisy-server/pkg/log"
	"encoding/json"
	"io"
	"net/url"
	"strings"
	"time"

	"github.com/duke-git/lancet/v2/random"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/datatypes"
)

func RequestLogMiddleware(logger *log.Logger) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// The configuration is initialized once per request
		trace, err := random.UUIdV4()
		if err != nil {
			logger.WithContext(ctx).Error("Failed to generate UUIDv4 for trace", zap.Error(err))
			return
		}
		logger.WithValue(ctx, zap.String("trace", trace))
		logger.WithValue(ctx, zap.String("request_method", ctx.Request.Method))
		logger.WithValue(ctx, zap.Any("request_headers", ctx.Request.Header))
		logger.WithValue(ctx, zap.String("request_url", ctx.Request.URL.String()))
		if ctx.Request.Body != nil {
			bodyBytes, err := ctx.GetRawData()
			if err != nil {
				logger.WithContext(ctx).Error("Failed to get raw request data", zap.Error(err))
			}
			ctx.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes)) // 关键点
			logger.WithValue(ctx, zap.String("request_params", string(bodyBytes)))
			// 将请求参数保存到context中
			ctx.Set("RequestParams", string(bodyBytes))
		}
		logger.WithContext(ctx).Info("Request")
		ctx.Next()
	}
}

func ResponseLogMiddleware(logger *log.Logger, sysLogRepo repository.SysLogRepository) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		blw := &bodyLogWriter{body: bytes.NewBufferString(""), ResponseWriter: ctx.Writer}
		ctx.Writer = blw
		startTime := time.Now()
		ctx.Next()
		duration := time.Since(startTime)

		// 记录响应日志
		responseBody := blw.body.String()
		logger.WithContext(ctx).Info("Response", zap.Any("response_body", responseBody), zap.Any("time", duration.String()))

		// 判断是否需要记录到数据库
		method := ctx.Request.Method
		if method != "POST" && method != "PUT" && method != "PATCH" && method != "DELETE" {
			return
		}

		// 从context中获取请求参数
		rawParams, _ := ctx.Get("RequestParams")
		paramsStr, ok := rawParams.(string)
		if !ok || paramsStr == "" {
			paramsStr = "{}"
		}

		// 处理敏感信息
		maskedParamsStr := paramsStr
		if paramsStr != "{}" {
			paramsMap := make(map[string]interface{})
			// Content-Type can be like "application/json; charset=utf-8", we only need the first part.
			contentType := strings.ToLower(strings.TrimSpace(strings.Split(ctx.ContentType(), ";")[0]))

			var parseErr error
			switch contentType {
			case gin.MIMEJSON:
				parseErr = json.Unmarshal([]byte(paramsStr), &paramsMap)
			case gin.MIMEPOSTForm:
				// application/x-www-form-urlencoded
				parsedQuery, err := url.ParseQuery(paramsStr)
				if err == nil {
					for k, v := range parsedQuery {
						if len(v) == 1 {
							paramsMap[k] = v[0]
						} else {
							paramsMap[k] = v
						}
					}
				} else {
					parseErr = err
				}
			default:
				// As a fallback, we attempt to parse it as JSON. This might be useful for other text-based bodies.
				parseErr = json.Unmarshal([]byte(paramsStr), &paramsMap)
			}

			if parseErr == nil {
				sensitiveKeys := []string{"password", "oldpass", "repass"}
				for _, key := range sensitiveKeys {
					if _, ok := paramsMap[key]; ok {
						paramsMap[key] = "******"
					}
				}
				if maskedBytes, err := json.Marshal(paramsMap); err == nil {
					maskedParamsStr = string(maskedBytes)
				} else {
					logger.WithContext(ctx).Warn("Failed to marshal masked params into JSON", zap.Error(err))
				}
			} else {
				logger.WithContext(ctx).Warn("Failed to parse request params for masking", zap.Error(parseErr), zap.String("contentType", contentType))
			}
		}

		// 创建并保存日志
		sysLogRepo.Create(ctx, &model.SysLog{
			UserId:    jwt.GetUserId(ctx),
			Path:      ctx.Request.URL.Path,
			Method:    method,
			Code:      ctx.Writer.Status(),
			Params:    datatypes.JSON(maskedParamsStr),
			Time:      duration.Seconds(),
			UserAgent: ctx.Request.UserAgent(),
			ClientIp:  ctx.ClientIP(),
		})
	}
}

type bodyLogWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w bodyLogWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}
