package v1

type WmsItemCreateParams struct {
	Name      string `json:"name" binding:"required,max=255" example:"iPhone 15"`
	Code      string `json:"code" binding:"required,max=64" example:"IP15001"`
	Summary   string `json:"summary" example:"苹果手机 iPhone 15 128GB"`
	Order     int    `json:"order" example:"1"`
	Status    bool   `json:"status" example:"true"`
	MetaId    uint   `json:"metaId" example:"1"`
	TenantId  uint   `json:"tenantId" example:"1"`
	CreatedBy string `json:"createdBy" example:"管理员"`
	UpdatedBy string `json:"updatedBy" example:"管理员"`
}

type WmsItemUpdateParams struct {
	WmsItemCreateParams
}

type WmsItemResponse struct {
	ID        uint   `json:"id"`
	Name      string `json:"name"`
	Code      string `json:"code"`
	Summary   string `json:"summary"`
	Order     int    `json:"order"`
	Status    bool   `json:"status"`
	MetaId    uint   `json:"metaId"`
	TenantId  uint   `json:"tenantId"`
	CreatedBy string `json:"createdBy"`
	UpdatedBy string `json:"updatedBy"`
	CreatedAt string `json:"createdAt"`
	UpdatedAt string `json:"updatedAt"`
}
