package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/jinzhu/copier"
	"golang.org/x/crypto/bcrypt"
)

type WmsStaffService interface {
	Create(ctx context.Context, req *v1.WmsStaffCreateParams) error
	Update(ctx context.Context, id uint, req *v1.WmsStaffUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.WmsStaffResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewWmsStaffService(
	service *Service,
	sysUserRepository repository.SysUserRepository,
	sysTenantRepository repository.SysTenantRepository,
) WmsStaffService {
	return &wmsStaffService{
		Service:             service,
		sysUserRepository:   sysUserRepository,
		sysTenantRepository: sysTenantRepository,
	}
}

type wmsStaffService struct {
	*Service
	sysUserRepository   repository.SysUserRepository
	sysTenantRepository repository.SysTenantRepository
}

// 员工相关方法实现
func (s *wmsStaffService) Create(ctx context.Context, req *v1.WmsStaffCreateParams) error {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}

	// 生成员工ID
	userId, err := s.sid.GenString()
	if err != nil {
		return err
	}

	staff := &model.SysUser{}
	if err := copier.Copy(staff, req); err != nil {
		return err
	}

	staff.UserId = userId
	staff.Password = string(hashedPassword)

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 租户ID
	staff.TenantId = user.TenantId

	// 默认角色ID
	staff.RoleIds = []int64{3}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysUserRepository.Create(ctx, staff); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsStaffService) Update(ctx context.Context, id uint, req *v1.WmsStaffUpdateParams) error {
	staff, err := s.sysUserRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if staff.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	if err := copier.Copy(staff, req); err != nil {
		return err
	}

	// 修改密码
	if req.Password != "" {
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
		if err != nil {
			return err
		}
		staff.Password = string(hashedPassword)
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysUserRepository.Update(ctx, staff); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsStaffService) Delete(ctx context.Context, id uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 是否是该租户数据
	staff, err := s.sysUserRepository.Get(ctx, id)
	if err != nil {
		return err
	}
	if staff.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysUserRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsStaffService) BatchDelete(ctx context.Context, ids []uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 获取目标记录
	staffs, err := s.sysUserRepository.GetByIds(ctx, ids)
	if err != nil {
		return err
	}

	// 过滤非本租户数据
	var newIds []uint
	for _, staff := range staffs {
		if staff.TenantId == user.TenantId {
			newIds = append(newIds, staff.ID)
		}
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysUserRepository.BatchDelete(ctx, newIds); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsStaffService) Get(ctx context.Context, id uint) (*v1.WmsStaffResponse, error) {
	staff, err := s.sysUserRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if staff.TenantId != user.TenantId {
		return nil, v1.ErrForbidden
	}

	response := &v1.WmsStaffResponse{}
	if err := copier.Copy(response, staff); err != nil {
		return nil, err
	}

	response.CreatedAt = staff.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = staff.UpdatedAt.Format(time.RFC3339)

	return response, nil
}

func (s *wmsStaffService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	params.AddFilter("tenant_id", user.TenantId)

	staffs, total, err := s.sysUserRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.WmsStaffResponse, 0, len(staffs))
	for _, staff := range staffs {
		response := &v1.WmsStaffResponse{}
		if err := copier.Copy(response, staff); err != nil {
			return nil, err
		}

		response.CreatedAt = staff.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = staff.UpdatedAt.Format(time.RFC3339)

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
