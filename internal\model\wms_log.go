package model

import "gorm.io/gorm"

// 库存日志表，用于管理库存的日志信息
type WmsLog struct {
	gorm.Model
	ItemId    uint    `gorm:"default:0; index; comment:物料ID"`
	SkuId     uint    `gorm:"default:0; index; comment:规格ID"`
	Type      uint    `gorm:"default:0; index; comment:类型 1-入库 2-出库 3-调拨 4-盘点 5-其它"`
	RelatedNo string  `gorm:"size:64; not null; index; comment:关联单号"`
	Num       float64 `gorm:"type:numeric(10,2); default:0; comment:历史库存"`
	Remain    float64 `gorm:"type:numeric(10,2); default:0; comment:剩余库存"`
	Summary   string  `gorm:"type:text; comment:备注"`
	TenantId  uint    `gorm:"default:0; index; comment:租户ID" copier:"-"`
	CreatedBy string  `gorm:"size:64; comment:创建人" copier:"-"`
	UpdatedBy string  `gorm:"size:64; comment:操作人" copier:"-"`
}

func (m *WmsLog) TableName() string {
	return "wms_log"
}
