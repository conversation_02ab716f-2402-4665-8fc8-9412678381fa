package v1

type WmsUnitCreateParams struct {
	Name      string `json:"name" binding:"required,max=64" example:"个"`
	Order     int    `json:"order" example:"1"`
	Status    bool   `json:"status" example:"true"`
	TenantId  uint   `json:"tenantId" example:"1"`
	CreatedBy string `json:"createdBy" example:"管理员"`
	UpdatedBy string `json:"updatedBy" example:"管理员"`
}

type WmsUnitUpdateParams struct {
	WmsUnitCreateParams
}

type WmsUnitResponse struct {
	ID        uint   `json:"id"`
	Name      string `json:"name"`
	Order     int    `json:"order"`
	Status    bool   `json:"status"`
	TenantId  uint   `json:"tenantId"`
	CreatedBy string `json:"createdBy"`
	UpdatedBy string `json:"updatedBy"`
	CreatedAt string `json:"createdAt"`
	UpdatedAt string `json:"updatedAt"`
}
