package middleware

import (
	"github.com/gin-gonic/gin"
	"net/http"
)

func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		method := c.Request.Method
		c.<PERSON>er("Access-Control-Allow-Origin", c.<PERSON>("Origin"))
		c<PERSON>("Access-Control-Allow-Credentials", "true")

		if method == "OPTIONS" {
			c<PERSON>("Access-Control-Allow-Methods", c<PERSON>("Access-Control-Request-Method"))
			c.<PERSON><PERSON>("Access-Control-Allow-Headers", c<PERSON><PERSON>("Access-Control-Request-Headers"))
			c<PERSON><PERSON>("Access-Control-Max-Age", "7200")
			c.<PERSON><PERSON><PERSON>(http.StatusNoContent)
			return
		}
		c.<PERSON>()
	}
}
