package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type SysDictRepository interface {
	Create(ctx context.Context, dict *model.SysDict) error
	Update(ctx context.Context, dict *model.SysDict) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.SysDict, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.SysDict, int64, error)
}

func NewSysDictRepository(
	repository *Repository,
) SysDictRepository {
	return &sysDictRepository{
		Repository: repository,
	}
}

type sysDictRepository struct {
	*Repository
}

func (r *sysDictRepository) Create(ctx context.Context, dict *model.SysDict) error {
	if err := r.DB(ctx).Create(dict).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysDictRepository) Update(ctx context.Context, dict *model.SysDict) error {
	if err := r.DB(ctx).Save(dict).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysDictRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.SysDict{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysDictRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.SysDict{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysDictRepository) Get(ctx context.Context, id uint) (*model.SysDict, error) {
	var dict model.SysDict
	if err := r.DB(ctx).First(&dict, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &dict, nil
}

func (r *sysDictRepository) List(ctx context.Context, params *pagination.Params) ([]*model.SysDict, int64, error) {
	var records []*model.SysDict
	var total int64

	db := r.DB(ctx).Model(&model.SysDict{})

	total, err := params.GetResult(db, &records, &model.SysDict{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
