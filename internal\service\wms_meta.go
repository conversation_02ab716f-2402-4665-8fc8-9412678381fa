package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/jinzhu/copier"
)

type WmsMetaService interface {
	Create(ctx context.Context, req *v1.WmsMetaCreateParams) error
	Update(ctx context.Context, id uint, req *v1.WmsMetaUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.WmsMetaResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewWmsMetaService(
	service *Service,
	wmsMetaRepository repository.WmsMetaRepository,
) WmsMetaService {
	return &wmsMetaService{
		Service:           service,
		wmsMetaRepository: wmsMetaRepository,
	}
}

type wmsMetaService struct {
	*Service
	wmsMetaRepository repository.WmsMetaRepository
}

// 物料分类相关方法实现
func (s *wmsMetaService) Create(ctx context.Context, req *v1.WmsMetaCreateParams) error {
	meta := &model.WmsMeta{}
	if err := copier.Copy(meta, req); err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 租户ID
	meta.TenantId = user.TenantId

	// 创建人
	if user.Nickname != "" {
		meta.CreatedBy = user.Nickname
	} else {
		meta.CreatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsMetaRepository.Create(ctx, meta); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsMetaService) Update(ctx context.Context, id uint, req *v1.WmsMetaUpdateParams) error {
	meta, err := s.wmsMetaRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if meta.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	if err := copier.Copy(meta, req); err != nil {
		return err
	}

	// 更新人
	if user.Nickname != "" {
		meta.UpdatedBy = user.Nickname
	} else {
		meta.UpdatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsMetaRepository.Update(ctx, meta); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsMetaService) Delete(ctx context.Context, id uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 是否是该租户数据
	meta, err := s.wmsMetaRepository.Get(ctx, id)
	if err != nil {
		return err
	}
	if meta.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsMetaRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsMetaService) BatchDelete(ctx context.Context, ids []uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 获取目标记录
	metas, err := s.wmsMetaRepository.GetByIds(ctx, ids)
	if err != nil {
		return err
	}
	
	// 过滤非本租户数据
	var newIds []uint
	for _, meta := range metas {
		if meta.TenantId == user.TenantId {
			newIds = append(newIds, meta.ID)
		}
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsMetaRepository.BatchDelete(ctx, newIds); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsMetaService) Get(ctx context.Context, id uint) (*v1.WmsMetaResponse, error) {
	meta, err := s.wmsMetaRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if meta.TenantId != user.TenantId {
		return nil, v1.ErrForbidden
	}

	response := &v1.WmsMetaResponse{}
	if err := copier.Copy(response, meta); err != nil {
		return nil, err
	}

	response.CreatedAt = meta.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = meta.UpdatedAt.Format(time.RFC3339)

	return response, nil
}

func (s *wmsMetaService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	params.AddFilter("tenant_id", user.TenantId)

	metas, total, err := s.wmsMetaRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.WmsMetaResponse, 0, len(metas))
	for _, meta := range metas {
		response := &v1.WmsMetaResponse{}
		if err := copier.Copy(response, meta); err != nil {
			return nil, err
		}

		response.CreatedAt = meta.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = meta.UpdatedAt.Format(time.RFC3339)

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
