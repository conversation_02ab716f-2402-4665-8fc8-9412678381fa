package v1

import "github.com/lib/pq"

type WmsPartnerCreateParams struct {
	Name        string        `json:"name" binding:"required,max=64" example:"阿里巴巴集团"`
	Type        pq.Int64Array `json:"type" swaggertype:"array,integer" example:"1,2" binding:"required"`
	Level       uint          `json:"level" example:"1"`
	Summary     string        `json:"summary" example:"知名互联网公司"`
	Contact     string        `json:"contact" example:"张三"`
	Phone       string        `json:"phone" example:"***********"`
	Email       string        `json:"email" binding:"omitempty,email" example:"<EMAIL>"`
	Area        string        `json:"area" example:"浙江省杭州市"`
	Address     string        `json:"address" example:"杭州市余杭区文一西路969号"`
	BankName    string        `json:"bankName" example:"中国工商银行"`
	BankAccount string        `json:"bankAccount" example:"1234567890123456789"`
	Order       int           `json:"order" example:"1"`
	Status      bool          `json:"status" example:"true"`
	TenantId    uint          `json:"tenantId" example:"1"`
	CreatedBy   string        `json:"createdBy" example:"管理员"`
	UpdatedBy   string        `json:"updatedBy" example:"管理员"`
}

type WmsPartnerUpdateParams struct {
	WmsPartnerCreateParams
}

type WmsPartnerResponse struct {
	ID          uint          `json:"id"`
	Name        string        `json:"name"`
	Type        pq.Int64Array `json:"type"`
	Level       uint          `json:"level"`
	Summary     string        `json:"summary"`
	Contact     string        `json:"contact"`
	Phone       string        `json:"phone"`
	Email       string        `json:"email"`
	Area        string        `json:"area"`
	Address     string        `json:"address"`
	BankName    string        `json:"bankName"`
	BankAccount string        `json:"bankAccount"`
	Order       int           `json:"order"`
	Status      bool          `json:"status"`
	TenantId    uint          `json:"tenantId"`
	CreatedBy   string        `json:"createdBy"`
	UpdatedBy   string        `json:"updatedBy"`
	CreatedAt   string        `json:"createdAt"`
	UpdatedAt   string        `json:"updatedAt"`
}
