//go:build wireinject
// +build wireinject

package wire

import (
	"daisy-server/internal/handler"
	"daisy-server/internal/job"
	"daisy-server/internal/repository"
	"daisy-server/internal/server"
	"daisy-server/internal/service"
	"daisy-server/pkg/app"
	"daisy-server/pkg/jwt"
	"daisy-server/pkg/log"
	"daisy-server/pkg/rbac"
	"daisy-server/pkg/server/http"
	"daisy-server/pkg/sid"

	"github.com/google/wire"
	"github.com/spf13/viper"
)

var repositorySet = wire.NewSet(
	repository.NewDB,
	repository.NewRedis,
	//repository.NewMongo,
	repository.NewRepository,
	repository.NewTransaction,
	repository.NewSysConfigRepository,
	repository.NewSysDictRepository,
	repository.NewSysMenuRepository,
	repository.NewSysApiRepository,
	repository.NewSysRoleRepository,
	repository.NewSysUserRepository,
	repository.NewSysTenantRepository,
	repository.NewSysLogRepository,
	repository.NewCmsMetaRepository,
	repository.NewCmsPostRepository,
	repository.NewWmsAreaRepository,
	repository.NewWmsPartnerRepository,
	repository.NewWmsReceiveRepository,
	repository.NewWmsItemRepository,
	repository.NewWmsMetaRepository,
	repository.NewWmsUnitRepository,
	repository.NewWmsSkuRepository,
	repository.NewWmsStockRepository,
	repository.NewWmsLogRepository,
)

var serviceSet = wire.NewSet(
	service.NewService,
	service.NewAuthService,
	service.NewUploadService,
	service.NewSysConfigService,
	service.NewSysDictService,
	service.NewSysMenuService,
	service.NewSysApiService,
	service.NewSysRoleService,
	service.NewSysUserService,
	service.NewSysTenantService,
	service.NewSysLogService,
	service.NewCmsMetaService,
	service.NewCmsPostService,
	service.NewWmsAreaService,
	service.NewWmsStaffService,
	service.NewWmsPartnerService,
	service.NewWmsReceiveService,
	service.NewWmsItemService,
	service.NewWmsMetaService,
	service.NewWmsUnitService,
	service.NewWmsSkuService,
	service.NewWmsStockService,
	service.NewWmsLogService,
)

var handlerSet = wire.NewSet(
	handler.NewHandler,
	handler.NewAuthHandler,
	handler.NewUploadHandler,
	handler.NewSysConfigHandler,
	handler.NewSysDictHandler,
	handler.NewSysMenuHandler,
	handler.NewSysApiHandler,
	handler.NewSysRoleHandler,
	handler.NewSysUserHandler,
	handler.NewSysTenantHandler,
	handler.NewSysLogHandler,
	handler.NewCmsMetaHandler,
	handler.NewCmsPostHandler,
	handler.NewWmsAreaHandler,
	handler.NewWmsStaffHandler,
	handler.NewWmsPartnerHandler,
	handler.NewWmsReceiveHandler,
	handler.NewWmsItemHandler,
	handler.NewWmsMetaHandler,
	handler.NewWmsUnitHandler,
	handler.NewWmsSkuHandler,
	handler.NewWmsStockHandler,
	handler.NewWmsLogHandler,
)

var jobSet = wire.NewSet(
	job.NewJob,
	job.NewUserJob,
)
var serverSet = wire.NewSet(
	server.NewHTTPServer,
	server.NewJobServer,
)

// build App
func newApp(
	httpServer *http.Server,
	jobServer *server.JobServer,
	// task *server.Task,
) *app.App {
	return app.NewApp(
		app.WithServer(httpServer, jobServer),
		app.WithName("daisy-server"),
	)
}

func NewWire(*viper.Viper, *log.Logger) (*app.App, func(), error) {
	panic(wire.Build(
		repositorySet,
		serviceSet,
		handlerSet,
		jobSet,
		serverSet,
		rbac.NewRBAC,
		sid.NewSid,
		jwt.NewJwt,
		newApp,
	))
}
