package v1

import (
	"gorm.io/datatypes"
)

type SysApiCreateParams struct {
	Path    string         `json:"path" binding:"required" example:"/system/user/list"`
	Method  string         `json:"method" binding:"required" example:"GET"`
	Tags    datatypes.JSON `json:"tags" swaggertype:"array,string" example:"标签1,标签2"`
	Summary string         `json:"summary" example:"获取用户列表"`
	Status  bool           `json:"status" example:"true"`
}

type SysApiUpdateParams struct {
	SysApiCreateParams
}

type SysApiResponse struct {
	ID        uint           `json:"id"`
	Path      string         `json:"path"`
	Method    string         `json:"method"`
	Tags      datatypes.JSON `json:"tags"`
	Summary   string         `json:"summary"`
	Status    bool           `json:"status"`
	CreatedAt string         `json:"createdAt"`
	UpdatedAt string         `json:"updatedAt"`
}
