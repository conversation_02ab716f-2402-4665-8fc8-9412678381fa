package service

import (
	"context"
	"fmt"
	"mime"
	"mime/multipart"
	"net/http"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

type UploadService interface {
	UploadImage(ctx context.Context, file *multipart.FileHeader) (string, string, error)
	UploadFile(ctx context.Context, file *multipart.FileHeader) (string, string, error)
	RemoveFile(ctx context.Context, fileName string) error
}

type uploadService struct {
	*Service
	conf *viper.Viper
	// 添加MinIO客户端缓存
	minioClient     *minio.Client
	minioInitErr    error
	minioClientOnce sync.Once
}

func NewUploadService(service *Service, conf *viper.Viper) UploadService {
	return &uploadService{
		Service: service,
		conf:    conf,
	}
}

// 创建MinIO客户端（使用单例模式避免重复创建）
func (s *uploadService) createMinioClient() (*minio.Client, error) {
	s.minioClientOnce.Do(func() {
		endpoint := s.conf.GetString("data.minio.endpoint")
		accessKey := s.conf.GetString("data.minio.access_key")
		secretKey := s.conf.GetString("data.minio.secret_key")
		secure := s.conf.GetBool("data.minio.secure")

		// 优化MinIO客户端配置
		transport := &http.Transport{
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 100,
			IdleConnTimeout:     90 * time.Second,
			DisableCompression:  true, // 禁用压缩可提高性能
		}

		// 初始化MinIO客户端
		var err error
		s.minioClient, err = minio.New(endpoint, &minio.Options{
			Creds:     credentials.NewStaticV4(accessKey, secretKey, ""),
			Secure:    secure, // 根据实际情况设置是否使用HTTPS
			Transport: transport,
		})
		if err != nil {
			s.logger.Error("创建MinIO客户端失败", zap.Error(err))
			s.minioInitErr = err
			return
		}

		bucketName := s.conf.GetString("data.minio.bucket")
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		// 检查桶是否存在，不存在则创建
		exists, err := s.minioClient.BucketExists(ctx, bucketName)
		if err != nil {
			s.logger.Error("检查桶是否存在失败", zap.Error(err))
			s.minioInitErr = err
			return
		}

		if !exists {
			err = s.minioClient.MakeBucket(ctx, bucketName, minio.MakeBucketOptions{})
			if err != nil {
				s.logger.Error("创建桶失败", zap.Error(err))
				s.minioInitErr = err
				return
			}
		}
	})

	return s.minioClient, s.minioInitErr
}

// UploadImage 上传图片到MinIO
func (s *uploadService) UploadImage(ctx context.Context, file *multipart.FileHeader) (string, string, error) {
	allowedTypes := []string{".jpg", ".jpeg", ".png", ".gif", ".webp"}
	return s.upload(ctx, file, "images", allowedTypes, 0)
}

// UploadFile 上传普通文件到MinIO
func (s *uploadService) UploadFile(ctx context.Context, file *multipart.FileHeader) (string, string, error) {
	allowedTypes := []string{".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".txt", ".zip", ".rar", ".7z", ".jpg", ".jpeg", ".png", ".gif", ".webp"}
	maxSize := int64(50 * 1024 * 1024)
	return s.upload(ctx, file, "files", allowedTypes, maxSize)
}

// upload 是处理文件上传的通用私有方法
func (s *uploadService) upload(ctx context.Context, file *multipart.FileHeader, folder string, allowedTypes []string, maxSize int64) (string, string, error) {
	// 获取文件扩展名
	ext := strings.ToLower(filepath.Ext(file.Filename))

	// 检查文件类型
	isAllowed := false
	for _, t := range allowedTypes {
		if ext == t {
			isAllowed = true
			break
		}
	}
	if !isAllowed {
		return "", "", fmt.Errorf("不支持的文件类型: %s", ext)
	}

	if maxSize > 0 && file.Size > maxSize {
		return "", "", fmt.Errorf("文件大小超过 %dMB", maxSize/1024/1024)
	}

	// 打开文件
	src, err := file.Open()
	if err != nil {
		s.logger.Error("打开文件失败", zap.Error(err))
		return "", "", err
	}
	defer src.Close()

	// 创建MinIO客户端
	minioClient, err := s.createMinioClient()
	if err != nil {
		return "", "", err
	}

	// 生成唯一的文件名
	fileName := fmt.Sprintf("%s/%d%s", folder, time.Now().UnixNano(), ext)
	bucketName := s.conf.GetString("data.minio.bucket")

	// 根据文件扩展名设置内容类型
	contentType := mime.TypeByExtension(ext)
	if contentType == "" {
		contentType = "application/octet-stream"
	}

	// 优化上传选项
	putOptions := minio.PutObjectOptions{
		ContentType:      contentType,
		PartSize:         10 * 1024 * 1024,        // 增加分片大小为10MB
		DisableMultipart: file.Size < 5*1024*1024, // 小于5MB的文件不使用分片上传
	}

	// 上传文件
	_, err = minioClient.PutObject(ctx, bucketName, fileName, src, file.Size, putOptions)
	if err != nil {
		s.logger.Error("上传文件到MinIO失败", zap.Error(err))
		return "", "", err
	}

	// 构建文件URL
	endpoint := s.conf.GetString("data.minio.endpoint")
	scheme := "http"
	if s.conf.GetBool("data.minio.secure") {
		scheme = "https"
	}
	fileURL := fmt.Sprintf("%s://%s/%s/%s", scheme, endpoint, bucketName, fileName)

	return fileURL, fileName, nil
}

// RemoveFile 从MinIO删除文件
func (s *uploadService) RemoveFile(ctx context.Context, fileName string) error {
	// 创建MinIO客户端
	minioClient, err := s.createMinioClient()
	if err != nil {
		return err
	}

	bucketName := s.conf.GetString("data.minio.bucket")

	// 删除文件
	err = minioClient.RemoveObject(ctx, bucketName, fileName, minio.RemoveObjectOptions{})
	if err != nil {
		s.logger.Error("从MinIO删除文件失败", zap.Error(err))
		return err
	}

	return nil
}
