package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type WmsItemRepository interface {
	Create(ctx context.Context, item *model.WmsItem) error
	Update(ctx context.Context, item *model.WmsItem) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.WmsItem, error)
	GetByIds(ctx context.Context, ids []uint) ([]*model.WmsItem, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.WmsItem, int64, error)
}

func NewWmsItemRepository(
	repository *Repository,
) WmsItemRepository {
	return &wmsItemRepository{
		Repository: repository,
	}
}

type wmsItemRepository struct {
	*Repository
}

func (r *wmsItemRepository) Create(ctx context.Context, item *model.WmsItem) error {
	if err := r.DB(ctx).Create(item).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsItemRepository) Update(ctx context.Context, item *model.WmsItem) error {
	if err := r.DB(ctx).Save(item).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsItemRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.WmsItem{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsItemRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.WmsItem{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsItemRepository) Get(ctx context.Context, id uint) (*model.WmsItem, error) {
	var item model.WmsItem
	if err := r.DB(ctx).First(&item, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &item, nil
}

func (r *wmsItemRepository) GetByIds(ctx context.Context, ids []uint) ([]*model.WmsItem, error) {
	var items []*model.WmsItem
	if len(ids) == 0 {
		return items, nil
	}
	
	if err := r.DB(ctx).Where("id IN ?", ids).Find(&items).Error; err != nil {
		return nil, err
	}
	return items, nil
}

func (r *wmsItemRepository) List(ctx context.Context, params *pagination.Params) ([]*model.WmsItem, int64, error) {
	var records []*model.WmsItem
	var total int64

	db := r.DB(ctx).Model(&model.WmsItem{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.WmsItem{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
