package model

import (
	"gorm.io/gorm"
)

// 盘点单物料明细表，用于管理仓库的盘点单物料明细信息
type WmsCheckSku struct {
	gorm.Model
	CheckId   uint    `gorm:"default:0; index; comment:盘点单ID"`
	ItemId    uint    `gorm:"default:0; index; comment:物料ID"`
	SkuId     uint    `gorm:"default:0; index; comment:规格ID"`
	Num       float64 `gorm:"type:numeric(10,2); default:0; comment:盘点库存"`
	Remain    float64 `gorm:"type:numeric(10,2); default:0; comment:实际库存"`
	Status    bool    `gorm:"default:false; index; comment:状态"`
	TenantId  uint    `gorm:"default:0; index; comment:租户ID" copier:"-"`
	CreatedBy string  `gorm:"size:64; comment:创建人" copier:"-"`
	UpdatedBy string  `gorm:"size:64; comment:操作人" copier:"-"`
}

func (m *WmsCheckSku) TableName() string {
	return "wms_check_sku"
}
