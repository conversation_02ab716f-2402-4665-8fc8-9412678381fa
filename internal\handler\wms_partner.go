package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type WmsPartnerHandler struct {
	*Handler
	wmsPartnerService service.WmsPartnerService
}

func NewWmsPartnerHandler(
	handler *Handler,
	wmsPartnerService service.WmsPartnerService,
) *WmsPartnerHandler {
	return &WmsPartnerHandler{
		Handler:           handler,
		wmsPartnerService: wmsPartnerService,
	}
}

// Create godoc
// @Summary 创建合作伙伴
// @Schemes
// @Description 创建新的合作伙伴记录
// @Tags 仓储模块,合作伙伴管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.WmsPartnerCreateParams true "合作伙伴信息"
// @Success 200 {object} v1.Response
// @Router /wms/partners [post]
func (h *WmsPartnerHandler) Create(ctx *gin.Context) {
	var req v1.WmsPartnerCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsPartnerService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新合作伙伴
// @Schemes
// @Description 更新指定ID的合作伙伴信息
// @Tags 仓储模块,合作伙伴管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "合作伙伴ID"
// @Param request body v1.WmsPartnerUpdateParams true "合作伙伴信息"
// @Success 200 {object} v1.Response
// @Router /wms/partners/{id} [patch]
func (h *WmsPartnerHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.WmsPartnerUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsPartnerService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除合作伙伴
// @Schemes
// @Description 删除指定ID的合作伙伴
// @Tags 仓储模块,合作伙伴管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "合作伙伴ID"
// @Success 200 {object} v1.Response
// @Router /wms/partners/{id} [delete]
func (h *WmsPartnerHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsPartnerService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除合作伙伴
// @Schemes
// @Description 批量删除指定IDs的合作伙伴
// @Tags 仓储模块,合作伙伴管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "合作伙伴IDs"
// @Success 200 {object} v1.Response
// @Router /wms/partners [delete]
func (h *WmsPartnerHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsPartnerService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取合作伙伴
// @Schemes
// @Description 获取指定ID的合作伙伴信息
// @Tags 仓储模块,合作伙伴管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "合作伙伴ID"
// @Success 200 {object} v1.Response{data=v1.WmsPartnerResponse}
// @Router /wms/partners/{id} [get]
func (h *WmsPartnerHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	partner, err := h.wmsPartnerService.Get(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, partner)
}

// List godoc
// @Summary 获取合作伙伴列表
// @Schemes
// @Description 分页获取合作伙伴列表
// @Tags 仓储模块,合作伙伴管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param status query bool false "状态筛选" example:"true"
// @Param type query string false "类型筛选" example:"1"
// @Param level query int false "级别筛选" example:"1"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.WmsPartnerResponse}}
// @Router /wms/partners [get]
func (h *WmsPartnerHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	// 状态筛选
	if status := ctx.DefaultQuery("status", ""); status != "" {
		params.AddFilter("status", status)
	}

	// 名称筛选
	if name := ctx.DefaultQuery("name", ""); name != "" {
		params.AddFilter("name_like", name)
	}

	// 类型筛选
	if partnerType := ctx.DefaultQuery("type", ""); partnerType != "" {
		params.AddFilter("type_any", partnerType)
	}

	// 级别筛选
	if level := ctx.DefaultQuery("level", ""); level != "" {
		params.AddFilter("level", level)
	}

	// 联系人筛选
	if contact := ctx.DefaultQuery("contact", ""); contact != "" {
		params.AddFilter("contact_like", contact)
	}

	// 手机号筛选
	if phone := ctx.DefaultQuery("phone", ""); phone != "" {
		params.AddFilter("phone_like", phone)
	}

	// 地区筛选
	if area := ctx.DefaultQuery("area", ""); area != "" {
		params.AddFilter("area_like", area)
	}

	result, err := h.wmsPartnerService.List(ctx, &params)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
