env: prod
http:
  host: 0.0.0.0
  #  host: 127.0.0.1
  port: 8000
security:
  api_sign:
    app_key: 123456
    app_security: 123456
  jwt:
    key: QQYnRFerJTSEcrfB89fw8prOaObmrch8
  rbac:
    model:
      request_definition:
        r: [sub, obj, act]
      policy_definition:
        p: [sub, obj, act]
      role_definition:
        g: [_, _]
      policy_effect:
        e: some(where (p.eft == allow))
      matchers:
        m: g(r.sub, p.sub) && r.obj == p.obj && r.act == p.act
    policy:
      - [p, super, "/*", "*"]
data:
  db:
    user:
      driver: postgres
      dsn: host=localhost user=postgres password=smilE4475 dbname=postgres port=5432 sslmode=disable TimeZone=Asia/Shanghai
  redis:
    addr: 127.0.0.1:6379
    password: smilE4475
    db: 0
    read_timeout: 0.2s
    write_timeout: 0.2s
  minio:
    endpoint: 127.0.0.1:9000
    access_key: minioadmin
    secret_key: smilE4475
    bucket: wms
log:
  log_level: info
  mode: file               #  file or console or both
  encoding: json           # json or console
  log_file_name: "./storage/logs/server.log"
  max_backups: 30
  max_age: 7
  max_size: 1024
  compress: true