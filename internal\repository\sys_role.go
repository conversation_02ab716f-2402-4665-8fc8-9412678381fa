package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type SysRoleRepository interface {
	Create(ctx context.Context, role *model.SysRole) error
	Update(ctx context.Context, role *model.SysRole) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.SysRole, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.SysRole, int64, error)
}

func NewSysRoleRepository(
	repository *Repository,
) SysRoleRepository {
	return &sysRoleRepository{
		Repository: repository,
	}
}

type sysRoleRepository struct {
	*Repository
}

func (r *sysRoleRepository) Create(ctx context.Context, role *model.SysRole) error {
	if err := r.DB(ctx).Create(role).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysRoleRepository) Update(ctx context.Context, role *model.SysRole) error {
	if err := r.DB(ctx).Save(role).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysRoleRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.SysRole{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysRoleRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.SysRole{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysRoleRepository) Get(ctx context.Context, id uint) (*model.SysRole, error) {
	var role model.SysRole
	if err := r.DB(ctx).First(&role, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &role, nil
}

func (r *sysRoleRepository) List(ctx context.Context, params *pagination.Params) ([]*model.SysRole, int64, error) {
	var records []*model.SysRole
	var total int64

	db := r.DB(ctx).Model(&model.SysRole{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.SysRole{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
