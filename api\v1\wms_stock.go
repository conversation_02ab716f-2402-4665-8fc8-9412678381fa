package v1

import "github.com/lib/pq"

type WmsStockCreateParams struct {
	ItemId    uint          `json:"itemId" binding:"required" example:"1"`
	SkuId     uint          `json:"skuId" binding:"required" example:"1"`
	AreaPath  pq.Int64Array `json:"areaPath" swaggertype:"array,integer" example:"1,2,3" binding:"required"`
	Num       float64       `json:"num" binding:"required" example:"100"`
	TenantId  uint          `json:"tenantId" example:"1"`
	CreatedBy string        `json:"createdBy" example:"管理员"`
	UpdatedBy string        `json:"updatedBy" example:"管理员"`
}

type WmsStockUpdateParams struct {
	WmsStockCreateParams
}

type WmsStockResponse struct {
	ID        uint             `json:"id"`
	ItemId    uint             `json:"itemId"`
	SkuId     uint             `json:"skuId"`
	AreaPath  pq.Int64Array    `json:"areaPath"`
	Num       float64          `json:"num"`
	TenantId  uint             `json:"tenantId"`
	CreatedBy string           `json:"createdBy"`
	UpdatedBy string           `json:"updatedBy"`
	CreatedAt string           `json:"createdAt"`
	UpdatedAt string           `json:"updatedAt"`
	Item      *WmsItemResponse `json:"item,omitempty"` // 物料信息
	Sku       *WmsSkuResponse  `json:"sku,omitempty"`  // SKU信息
}
