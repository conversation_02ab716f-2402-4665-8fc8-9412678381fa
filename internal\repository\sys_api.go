package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type SysApiRepository interface {
	Create(ctx context.Context, api *model.SysApi) error
	Update(ctx context.Context, api *model.SysApi) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.SysApi, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.SysApi, int64, error)
	ClearAll(ctx context.Context) error
}

func NewSysApiRepository(
	repository *Repository,
) SysApiRepository {
	return &sysApiRepository{
		Repository: repository,
	}
}

type sysApiRepository struct {
	*Repository
}

func (r *sysApiRepository) Create(ctx context.Context, api *model.SysApi) error {
	if err := r.DB(ctx).Create(api).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysApiRepository) Update(ctx context.Context, api *model.SysApi) error {
	if err := r.DB(ctx).Save(api).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysApiRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.SysApi{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysApiRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.SysApi{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysApiRepository) Get(ctx context.Context, id uint) (*model.SysApi, error) {
	var api model.SysApi
	if err := r.DB(ctx).First(&api, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &api, nil
}

func (r *sysApiRepository) List(ctx context.Context, params *pagination.Params) ([]*model.SysApi, int64, error) {
	var records []*model.SysApi
	var total int64

	db := r.DB(ctx).Model(&model.SysApi{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.SysApi{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}

func (r *sysApiRepository) ClearAll(ctx context.Context) error {
	return r.DB(ctx).Exec("TRUNCATE TABLE sys_api RESTART IDENTITY CASCADE").Error
}
