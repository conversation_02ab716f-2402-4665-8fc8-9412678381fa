package v1

import (
	"github.com/lib/pq"
	"gorm.io/datatypes"
)

type CmsPostCreateParams struct {
	Title    string             `json:"title" binding:"required,max=64" example:"文章标题"`
	Slug     string             `json:"slug" example:"article-slug"`
	Summary  string             `json:"summary" example:"文章摘要"`
	Content  string             `json:"content" example:"文章内容"`
	Cover    string             `json:"cover" example:"https://example.com/cover.jpg"`
	Author   string             `json:"author" example:"作者名"`
	From     string             `json:"from" example:"来源"`
	Password string             `json:"password" example:"访问密码"`
	Tags     datatypes.JSON     `json:"tags" swaggertype:"array,string" example:"标签1,标签2"`
	Files    []UploadFileParams `json:"files"`
	Order    int                `json:"order" example:"1"`
	Flag     pq.Int64Array      `json:"flag" example:"1,2"`
	Status   bool               `json:"status" example:"true"`
	MetaId   uint               `json:"metaId" binding:"required" example:"1"`
}

type CmsPostUpdateParams struct {
	CmsPostCreateParams
}

type CmsPostResponse struct {
	ID        uint               `json:"id"`
	Title     string             `json:"title"`
	Slug      string             `json:"slug"`
	Summary   string             `json:"summary"`
	Content   string             `json:"content"`
	Cover     string             `json:"cover"`
	Author    string             `json:"author"`
	From      string             `json:"from"`
	Password  string             `json:"password"`
	Tags      datatypes.JSON     `json:"tags"`
	Files     []UploadFileParams `json:"files"`
	Order     int                `json:"order"`
	Flag      pq.Int64Array      `json:"flag"`
	Status    bool               `json:"status"`
	MetaId    uint               `json:"metaId"`
	CreatedAt string             `json:"createdAt"`
	UpdatedAt string             `json:"updatedAt"`
}
