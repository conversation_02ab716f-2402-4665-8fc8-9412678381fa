package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"
	"fmt"

	"gorm.io/gorm"
)

type WmsMetaRepository interface {
	Create(ctx context.Context, meta *model.WmsMeta) error
	Update(ctx context.Context, meta *model.WmsMeta) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.WmsMeta, error)
	GetByIds(ctx context.Context, ids []uint) ([]*model.WmsMeta, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.WmsMeta, int64, error)
	GetPathIds(ctx context.Context, metaId uint) ([]uint, error)
}

func NewWmsMetaRepository(
	repository *Repository,
) WmsMetaRepository {
	return &wmsMetaRepository{
		Repository: repository,
	}
}

type wmsMetaRepository struct {
	*Repository
}

func (r *wmsMetaRepository) Create(ctx context.Context, meta *model.WmsMeta) error {
	if err := r.DB(ctx).Create(meta).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsMetaRepository) Update(ctx context.Context, meta *model.WmsMeta) error {
	if err := r.DB(ctx).Save(meta).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsMetaRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.WmsMeta{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsMetaRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.WmsMeta{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsMetaRepository) Get(ctx context.Context, id uint) (*model.WmsMeta, error) {
	var meta model.WmsMeta
	if err := r.DB(ctx).First(&meta, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &meta, nil
}

func (r *wmsMetaRepository) GetByIds(ctx context.Context, ids []uint) ([]*model.WmsMeta, error) {
	var metas []*model.WmsMeta
	if len(ids) == 0 {
		return metas, nil
	}

	if err := r.DB(ctx).Where("id IN ?", ids).Find(&metas).Error; err != nil {
		return nil, err
	}
	return metas, nil
}

func (r *wmsMetaRepository) List(ctx context.Context, params *pagination.Params) ([]*model.WmsMeta, int64, error) {
	var records []*model.WmsMeta
	var total int64

	db := r.DB(ctx).Model(&model.WmsMeta{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.WmsMeta{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}

// 获取包含指定ID的所有分类ID（包括自身和所有子分类）
func (r *wmsMetaRepository) GetPathIds(ctx context.Context, metaId uint) ([]uint, error) {
	var metas []*model.WmsMeta
	var ids []uint

	// 查询所有 parent_path 包含 metaId 的分类，以及 metaId 本身
	err := r.DB(ctx).Where("id = ? OR parent_path @> ?", metaId, fmt.Sprintf("{%d}", metaId)).Find(&metas).Error
	if err != nil {
		return nil, err
	}

	for _, meta := range metas {
		ids = append(ids, meta.ID)
	}

	return ids, nil
}
