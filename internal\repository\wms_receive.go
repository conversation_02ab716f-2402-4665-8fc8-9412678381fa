package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type WmsReceiveRepository interface {
	Create(ctx context.Context, receive *model.WmsReceive) error
	Update(ctx context.Context, receive *model.WmsReceive) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.WmsReceive, error)
	GetByIds(ctx context.Context, ids []uint) ([]*model.WmsReceive, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.WmsReceive, int64, error)
}

func NewWmsReceiveRepository(
	repository *Repository,
) WmsReceiveRepository {
	return &wmsReceiveRepository{
		Repository: repository,
	}
}

type wmsReceiveRepository struct {
	*Repository
}

func (r *wmsReceiveRepository) Create(ctx context.Context, receive *model.WmsReceive) error {
	return r.DB(ctx).Create(receive).Error
}

func (r *wmsReceiveRepository) Update(ctx context.Context, receive *model.WmsReceive) error {
	return r.DB(ctx).Save(receive).Error
}

func (r *wmsReceiveRepository) Delete(ctx context.Context, id uint) error {
	return r.DB(ctx).Delete(&model.WmsReceive{}, id).Error
}

func (r *wmsReceiveRepository) BatchDelete(ctx context.Context, ids []uint) error {
	return r.DB(ctx).Delete(&model.WmsReceive{}, ids).Error
}

func (r *wmsReceiveRepository) Get(ctx context.Context, id uint) (*model.WmsReceive, error) {
	var receive model.WmsReceive
	if err := r.DB(ctx).First(&receive, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &receive, nil
}

func (r *wmsReceiveRepository) GetByIds(ctx context.Context, ids []uint) ([]*model.WmsReceive, error) {
	var receives []*model.WmsReceive
	if err := r.DB(ctx).Where("id IN ?", ids).Find(&receives).Error; err != nil {
		return nil, err
	}
	return receives, nil
}

func (r *wmsReceiveRepository) List(ctx context.Context, params *pagination.Params) ([]*model.WmsReceive, int64, error) {
	var receives []*model.WmsReceive
	var total int64

	query := r.DB(ctx).Model(&model.WmsReceive{})

	// 应用过滤器
	query = params.ApplyFilters(query)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 应用排序和分页
	query = params.ApplySortAndPagination(query)

	// 获取数据
	if err := query.Find(&receives).Error; err != nil {
		return nil, 0, err
	}

	return receives, total, nil
}
