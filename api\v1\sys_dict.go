package v1

type SysDictOption struct {
	ID     string `json:"id"`
	Type   string `json:"type"`
	Label  string `json:"label"`
	Value  string `json:"value"`
	Status bool   `json:"status"`
}

type SysDictCreateParams struct {
	Name    string          `json:"name" binding:"required,max=64" example:"性别"`
	Code    string          `json:"code" binding:"required,max=64" example:"gender"`
	Summary string          `json:"summary" example:"性别字典"`
	Status  bool            `json:"status" example:"true"`
	Options []SysDictOption `json:"options"`
}

type SysDictUpdateParams struct {
	SysDictCreateParams
}

type SysDictResponse struct {
	ID        uint            `json:"id"`
	Name      string          `json:"name"`
	Code      string          `json:"code"`
	Summary   string          `json:"summary"`
	Status    bool            `json:"status"`
	Options   []SysDictOption `json:"options"`
	CreatedAt string          `json:"createdAt"`
	UpdatedAt string          `json:"updatedAt"`
}
