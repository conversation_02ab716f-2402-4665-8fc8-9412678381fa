package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type WmsPartnerRepository interface {
	Create(ctx context.Context, partner *model.WmsPartner) error
	Update(ctx context.Context, partner *model.WmsPartner) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.WmsPartner, error)
	GetByIds(ctx context.Context, ids []uint) ([]*model.WmsPartner, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.WmsPartner, int64, error)
}

func NewWmsPartnerRepository(
	repository *Repository,
) WmsPartnerRepository {
	return &wmsPartnerRepository{
		Repository: repository,
	}
}

type wmsPartnerRepository struct {
	*Repository
}

func (r *wmsPartnerRepository) Create(ctx context.Context, partner *model.WmsPartner) error {
	if err := r.DB(ctx).Create(partner).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsPartnerRepository) Update(ctx context.Context, partner *model.WmsPartner) error {
	if err := r.DB(ctx).Save(partner).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsPartnerRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.WmsPartner{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsPartnerRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.WmsPartner{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsPartnerRepository) Get(ctx context.Context, id uint) (*model.WmsPartner, error) {
	var partner model.WmsPartner
	if err := r.DB(ctx).First(&partner, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &partner, nil
}

func (r *wmsPartnerRepository) GetByIds(ctx context.Context, ids []uint) ([]*model.WmsPartner, error) {
	var partners []*model.WmsPartner
	if len(ids) == 0 {
		return partners, nil
	}
	
	if err := r.DB(ctx).Where("id IN ?", ids).Find(&partners).Error; err != nil {
		return nil, err
	}
	return partners, nil
}

func (r *wmsPartnerRepository) List(ctx context.Context, params *pagination.Params) ([]*model.WmsPartner, int64, error) {
	var records []*model.WmsPartner
	var total int64

	db := r.DB(ctx).Model(&model.WmsPartner{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.WmsPartner{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
