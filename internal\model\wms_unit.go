package model

import "gorm.io/gorm"

// 物料单位表，用于管理物料的单位信息
type WmsUnit struct {
	gorm.Model
	Name      string `gorm:"size:64; not null; index; comment:名称"`
	Order     int    `gorm:"default:0; index; comment:排序"`
	Status    bool   `gorm:"default:false; index; comment:状态"`
	TenantId  uint   `gorm:"default:0; index; comment:租户ID" copier:"-"`
	CreatedBy string `gorm:"size:64; comment:创建人" copier:"-"`
	UpdatedBy string `gorm:"size:64; comment:操作人" copier:"-"`
}

func (m *WmsUnit) TableName() string {
	return "wms_unit"
}
