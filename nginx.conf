server {
    listen 443 ssl;
    server_name qindong.kscss.com;
    root /usr/www/kscss/qindong;

    charset utf-8;

    ssl_certificate cert/kscss.com.pem;
    ssl_certificate_key cert/kscss.com.key;

    ssl_session_cache shared:SSL:1m;
    ssl_session_timeout 5m;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
    ssl_protocols TLSv1.1 TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;

    location / {
        root /usr/www/kscss/qindong/web;
        try_files $uri $uri/ /index.html;
    }

    location /admin {
        try_files $uri $uri/ /admin/index.html;
    }

    # Reverse proxy /api to Backend Gin RESTful interface
    location /api/ {
        proxy_pass http://************:8000/v1/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Reverse proxy /webapi to Frondend Gin RESTful interface
    location /webapi/ {
        proxy_pass http://************:8001/v1/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Reverse proxy /oss to Minio object storage
    location /oss/ {
        proxy_pass http://************:9000/qindong/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
