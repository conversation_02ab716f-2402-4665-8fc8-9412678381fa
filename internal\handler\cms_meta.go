package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type CmsMetaHandler struct {
	*Handler
	cmsMetaService service.CmsMetaService
}

func NewCmsMetaHandler(
	handler *Handler,
	cmsMetaService service.CmsMetaService,
) *CmsMetaHandler {
	return &CmsMetaHandler{
		Handler:        handler,
		cmsMetaService: cmsMetaService,
	}
}

// Create godoc
// @Summary 创建栏目
// @Schemes
// @Description 创建新的栏目记录
// @Tags 内容模块,栏目管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.CmsMetaCreateParams true "栏目信息"
// @Success 200 {object} v1.Response
// @Router /cms/metas [post]
func (h *CmsMetaHandler) Create(ctx *gin.Context) {
	var req v1.CmsMetaCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.cmsMetaService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新栏目
// @Schemes
// @Description 更新指定ID的栏目信息
// @Tags 内容模块,栏目管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "栏目ID"
// @Param request body v1.CmsMetaUpdateParams true "栏目信息"
// @Success 200 {object} v1.Response
// @Router /cms/metas/{id} [patch]
func (h *CmsMetaHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.CmsMetaUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.cmsMetaService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除栏目
// @Schemes
// @Description 删除指定ID的栏目
// @Tags 内容模块,栏目管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "栏目ID"
// @Success 200 {object} v1.Response
// @Router /cms/metas/{id} [delete]
func (h *CmsMetaHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.cmsMetaService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除栏目
// @Schemes
// @Description 批量删除指定IDs的栏目
// @Tags 内容模块,栏目管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "栏目IDs"
// @Success 200 {object} v1.Response
// @Router /cms/metas [delete]
func (h *CmsMetaHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.cmsMetaService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取栏目
// @Schemes
// @Description 获取指定ID的栏目信息
// @Tags 内容模块,栏目管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "栏目ID"
// @Success 200 {object} v1.Response{data=v1.CmsMetaResponse}
// @Router /cms/metas/{id} [get]
func (h *CmsMetaHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	meta, err := h.cmsMetaService.Get(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, meta)
}

// List godoc
// @Summary 获取栏目列表
// @Schemes
// @Description 分页获取栏目列表
// @Tags 内容模块,栏目管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param status query bool false "状态筛选" example:"true"
// @Param parentId query int false "父级ID筛选" example:"0"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.CmsMetaResponse}}
// @Router /cms/metas [get]
func (h *CmsMetaHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	// 状态筛选
	if status := ctx.DefaultQuery("status", ""); status != "" {
		params.AddFilter("status", status)
	}

	// 名称筛选
	if name := ctx.DefaultQuery("name", ""); name != "" {
		params.AddFilter("name_like", name)
	}

	// 别名筛选
	if slug := ctx.DefaultQuery("slug", ""); slug != "" {
		params.AddFilter("slug_like", slug)
	}

	result, err := h.cmsMetaService.List(ctx, &params)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
