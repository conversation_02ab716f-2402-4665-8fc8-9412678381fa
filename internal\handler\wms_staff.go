package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type WmsStaffHandler struct {
	*Handler
	wmsStaffService service.WmsStaffService
}

func NewWmsStaffHandler(
	handler *Handler,
	wmsStaffService service.WmsStaffService,
) *WmsStaffHandler {
	return &WmsStaffHandler{
		Handler:         handler,
		wmsStaffService: wmsStaffService,
	}
}

// Create godoc
// @Summary 创建员工
// @Schemes
// @Description 创建新的员工
// @Tags 仓储模块,员工管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.WmsStaffCreateParams true "员工信息"
// @Success 200 {object} v1.Response
// @Router /wms/staffs [post]
func (h *WmsStaffHandler) Create(ctx *gin.Context) {
	var req v1.WmsStaffCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsStaffService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新员工
// @Schemes
// @Description 更新指定ID的员工信息
// @Tags 仓储模块,员工管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "员工ID"
// @Param request body v1.WmsStaffUpdateParams true "员工信息"
// @Success 200 {object} v1.Response
// @Router /wms/staffs/{id} [patch]
func (h *WmsStaffHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.WmsStaffUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsStaffService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除员工
// @Schemes
// @Description 删除指定ID的员工
// @Tags 仓储模块,员工管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "员工ID"
// @Success 200 {object} v1.Response
// @Router /wms/staffs/{id} [delete]
func (h *WmsStaffHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsStaffService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除员工
// @Schemes
// @Description 批量删除指定IDs的员工
// @Tags 仓储模块,员工管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "员工IDs"
// @Success 200 {object} v1.Response
// @Router /wms/staffs [delete]
func (h *WmsStaffHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsStaffService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取员工
// @Schemes
// @Description 获取指定ID的员工信息
// @Tags 仓储模块,员工管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "员工ID"
// @Param _expand query string false "展开关联信息，支持: tenant" example:"tenant"
// @Success 200 {object} v1.Response{data=v1.WmsStaffResponse}
// @Router /wms/staffs/{id} [get]
func (h *WmsStaffHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	staff, err := h.wmsStaffService.Get(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, staff)
}

// List godoc
// @Summary 获取员工列表
// @Schemes
// @Description 分页获取员工列表
// @Tags 仓储模块,员工管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param status query bool false "状态筛选" example:"true"
// @Param username query string false "员工名筛选" example:"admin"
// @Param nickname query string false "昵称筛选" example:"管理员"
// @Param phone query string false "手机号筛选" example:"13800138000"
// @Param email query string false "邮箱筛选" example:"<EMAIL>"
// @Param _expand query string false "展开关联信息，支持: tenant" example:"tenant"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.WmsStaffResponse}}
// @Router /wms/staffs [get]
func (h *WmsStaffHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	// 全文索引
	if q := ctx.DefaultQuery("q", ""); q != "" {
		params.Query = q
	}

	// 状态筛选
	if status := ctx.DefaultQuery("status", ""); status != "" {
		params.AddFilter("status", status)
	}

	// 员工名筛选
	if username := ctx.DefaultQuery("username", ""); username != "" {
		params.AddFilter("username_like", username)
	}

	// 昵称筛选
	if nickname := ctx.DefaultQuery("nickname", ""); nickname != "" {
		params.AddFilter("nickname_like", nickname)
	}

	// 手机号筛选
	if phone := ctx.DefaultQuery("phone", ""); phone != "" {
		params.AddFilter("phone", phone)
	}

	// 邮箱筛选
	if email := ctx.DefaultQuery("email", ""); email != "" {
		params.AddFilter("email", email)
	}

	result, err := h.wmsStaffService.List(ctx, &params)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
