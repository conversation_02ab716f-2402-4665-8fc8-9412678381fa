package model

import (
	"github.com/lib/pq"
	"gorm.io/gorm"
)

// 角色表，用于管理用户的角色信息
type SysRole struct {
	gorm.Model
	Name    string        `gorm:"size:64; not null; unique; comment:名称"`
	Code    string        `gorm:"size:64; not null; unique; comment:编码"`
	Summary string        `gorm:"type:text; comment:描述"`
	Status  bool          `gorm:"default:false; index; comment:状态"`
	Home    string        `gorm:"size:255; default:home; comment:首页"`
	MenuIds pq.Int64Array `gorm:"type:integer[]; comment:菜单IDs"`
	ApiIds  pq.Int64Array `gorm:"type:integer[]; comment:接口IDs"`
}

func (m *SysRole) TableName() string {
	return "sys_role"
}
