# 分页包使用指南

这个分页包提供了类似 json-server 的分页、排序和筛选功能，可以方便地应用于 GORM 查询。

## 特性

- 支持基于页码/每页数量的分页方式
- 支持多字段排序
- 支持全文搜索（自动对所有字符串字段进行模糊匹配）
- 支持多种筛选条件：等于、大于等于、小于等于、不等于、模糊匹配

## 参数说明

### 分页参数

- `_page`: 当前页码，从 1 开始
- `_limit`: 每页数量，默认为 10

### 排序参数

- `_sort`: 排序字段，多个字段用逗号分隔
- `_order`: 排序方式，asc（升序）或 desc（降序），多个方式用逗号分隔

### 筛选参数

- `q`: 全文搜索关键词（会自动对模型的所有字符串字段进行模糊匹配）
- 其他字段: 可以通过 Filters 添加任意字段进行筛选

## 核心结构体

### Params 结构体

```go
type Params struct {
    // 分页参数
    Page  int `form:"_page" json:"_page"`   // 当前页码
    Limit int `form:"_limit" json:"_limit"` // 每页数量

    // 排序参数
    Sort  string `form:"_sort" json:"_sort"`   // 排序字段，多个字段用逗号分隔
    Order string `form:"_order" json:"_order"` // 排序方式，asc或desc，多个方式用逗号分隔

    // 筛选参数
    Query   string                 `form:"q" json:"q"` // 全文搜索关键词
    Filters map[string]interface{} `form:"-" json:"-"` // 其他筛选条件
}
```

### Result 结构体

```go
type Result struct {
    Records interface{} `json:"records"` // 数据列表
    Total   int64       `json:"total"`   // 总记录数
}
```

## 核心方法

### Apply

```go
func (p *Params) Apply(db *gorm.DB, model interface{}) *gorm.DB
```

将分页、排序和筛选条件应用到 GORM 查询中，返回处理后的查询对象。

### SetDefaults

```go
func (p *Params) SetDefaults()
```

设置默认参数值，如果页码小于等于 0，设置为 1；如果每页数量小于等于 0，设置为 10。

### AddFilter

```go
func (p *Params) AddFilter(key string, value interface{})
```

添加筛选条件，支持多种筛选方式。

### GetResult

```go
func (p *Params) GetResult(db *gorm.DB, dest interface{}) (*Result, error)
```

获取分页结果，包含数据列表和总记录数。

## 使用示例

### 基本用法

```go
import (
    "gorm.io/gorm"
    "logistics-server/pkg/pagination"
)

func ListUsers(db *gorm.DB, params *pagination.Params) (*pagination.Result, error) {
    var users []*User
    
    // 设置默认值
    params.SetDefaults()
    
    // 添加筛选条件
    if params.Query != "" {
        // 全文搜索会自动应用到所有字符串字段
        // 无需手动添加筛选条件
    }
    
    // 获取分页结果
    result, err := params.GetResult(db.Model(&User{}), &users)
    if err != nil {
        return nil, err
    }
    
    return result, nil
}
```

### 特殊筛选条件

```go
// 等于
params.AddFilter("status", 1)

// 大于等于
params.AddFilter("age_gte", 18)

// 小于等于
params.AddFilter("age_lte", 60)

// 不等于
params.AddFilter("role_ne", "admin")

// 模糊匹配
params.AddFilter("name_like", "张")
```

## 全文搜索实现说明

当设置了 `q` 参数时，全文搜索会自动对模型的所有字符串字段进行 LIKE 查询。系统会：

1. 通过反射获取模型的所有字段
2. 分析每个字段的 GORM 标签，获取数据库列名
3. 对所有字符串类型的字段生成 LIKE 查询条件
4. 将所有条件用 OR 连接，形成完整的查询

## 完整示例

```go
func (r *userRepository) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
    var users []*model.User
    
    db := r.DB(ctx).Model(&model.User{})

    // 添加额外的筛选条件
    if params.Query != "" {
        // 系统会自动对所有字符串字段进行模糊匹配
        // 如果需要限制只对特定字段搜索，可以不设置 Query，而是手动添加筛选条件：
        params.AddFilter("username_like", params.Query)
        params.AddFilter("nickname_like", params.Query)
        params.AddFilter("email_like", params.Query)
    }
    
    // 处理状态筛选
    if statusParam := ctx.Query("status"); statusParam != "" {
        params.AddFilter("status", params.Status)
    }

    // 获取分页结果
    return params.GetResult(db, &users)
}
``` 

## 在处理程序中使用

```go
func (h *Handler) ListUsers(ctx *gin.Context) {
    var params pagination.Params
    if err := ctx.ShouldBindQuery(&params); err != nil {
        // 处理错误
        return
    }

    result, err := h.userService.ListUsers(ctx, &params)
    if err != nil {
        // 处理错误
        return
    }

    // 返回结果
    ctx.JSON(http.StatusOK, result)
}
``` 