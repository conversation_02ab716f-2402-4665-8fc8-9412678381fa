package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/jinzhu/copier"
)

type CmsMetaService interface {
	Create(ctx context.Context, req *v1.CmsMetaCreateParams) error
	Update(ctx context.Context, id uint, req *v1.CmsMetaUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.CmsMetaResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewCmsMetaService(
	service *Service,
	cmsMetaRepository repository.CmsMetaRepository,
) CmsMetaService {
	return &cmsMetaService{
		Service:           service,
		cmsMetaRepository: cmsMetaRepository,
	}
}

type cmsMetaService struct {
	*Service
	cmsMetaRepository repository.CmsMetaRepository
}

// 栏目相关方法实现
func (s *cmsMetaService) Create(ctx context.Context, req *v1.CmsMetaCreateParams) error {
	meta := &model.CmsMeta{}
	if err := copier.Copy(meta, req); err != nil {
		return err
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.cmsMetaRepository.Create(ctx, meta); err != nil {
			return err
		}
		return nil
	})
}

func (s *cmsMetaService) Update(ctx context.Context, id uint, req *v1.CmsMetaUpdateParams) error {
	meta, err := s.cmsMetaRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	if err := copier.Copy(meta, req); err != nil {
		return err
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.cmsMetaRepository.Update(ctx, meta); err != nil {
			return err
		}
		return nil
	})
}

func (s *cmsMetaService) Delete(ctx context.Context, id uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.cmsMetaRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *cmsMetaService) BatchDelete(ctx context.Context, ids []uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.cmsMetaRepository.BatchDelete(ctx, ids); err != nil {
			return err
		}
		return nil
	})
}

func (s *cmsMetaService) Get(ctx context.Context, id uint) (*v1.CmsMetaResponse, error) {
	meta, err := s.cmsMetaRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	response := &v1.CmsMetaResponse{}
	if err := copier.Copy(response, meta); err != nil {
		return nil, err
	}

	response.CreatedAt = meta.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = meta.UpdatedAt.Format(time.RFC3339)

	return response, nil
}

func (s *cmsMetaService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	metas, total, err := s.cmsMetaRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.CmsMetaResponse, 0, len(metas))
	for _, meta := range metas {
		response := &v1.CmsMetaResponse{}
		if err := copier.Copy(response, meta); err != nil {
			return nil, err
		}

		response.CreatedAt = meta.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = meta.UpdatedAt.Format(time.RFC3339)

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
