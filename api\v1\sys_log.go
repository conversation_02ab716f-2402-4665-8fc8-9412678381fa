package v1

import "gorm.io/datatypes"

type SysLogResponse struct {
	ID        uint           `json:"id"`
	UserId    string         `json:"userId"`
	Path      string         `json:"path"`
	Method    string         `json:"method"`
	Code      int            `json:"code"`
	Params    datatypes.JSON `json:"params"`
	Time      float64        `json:"time"`
	UserAgent string         `json:"userAgent"`
	ClientIp  string         `json:"clientIp"`
	CreatedAt string         `json:"createdAt"`
	UpdatedAt string         `json:"updatedAt"`
}
