package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type WmsUnitHandler struct {
	*Handler
	wmsUnitService service.WmsUnitService
}

func NewWmsUnitHandler(
	handler *Handler,
	wmsUnitService service.WmsUnitService,
) *WmsUnitHandler {
	return &WmsUnitHandler{
		Handler:        handler,
		wmsUnitService: wmsUnitService,
	}
}

// Create godoc
// @Summary 创建物料单位
// @Schemes
// @Description 创建新的物料单位记录
// @Tags 仓储模块,物料管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.WmsUnitCreateParams true "物料单位信息"
// @Success 200 {object} v1.Response
// @Router /wms/units [post]
func (h *WmsUnitHandler) Create(ctx *gin.Context) {
	var req v1.WmsUnitCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsUnitService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新物料单位
// @Schemes
// @Description 更新指定ID的物料单位信息
// @Tags 仓储模块,物料管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "物料单位ID"
// @Param request body v1.WmsUnitUpdateParams true "物料单位信息"
// @Success 200 {object} v1.Response
// @Router /wms/units/{id} [patch]
func (h *WmsUnitHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.WmsUnitUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsUnitService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除物料单位
// @Schemes
// @Description 删除指定ID的物料单位
// @Tags 仓储模块,物料管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "物料单位ID"
// @Success 200 {object} v1.Response
// @Router /wms/units/{id} [delete]
func (h *WmsUnitHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsUnitService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除物料单位
// @Schemes
// @Description 批量删除指定IDs的物料单位
// @Tags 仓储模块,物料管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "物料单位IDs"
// @Success 200 {object} v1.Response
// @Router /wms/units [delete]
func (h *WmsUnitHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsUnitService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取物料单位
// @Schemes
// @Description 获取指定ID的物料单位信息
// @Tags 仓储模块,物料管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "物料单位ID"
// @Success 200 {object} v1.Response{data=v1.WmsUnitResponse}
// @Router /wms/units/{id} [get]
func (h *WmsUnitHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	unit, err := h.wmsUnitService.Get(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, unit)
}

// List godoc
// @Summary 获取物料单位列表
// @Schemes
// @Description 分页获取物料单位列表
// @Tags 仓储模块,物料管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param status query bool false "状态筛选" example:"true"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.WmsUnitResponse}}
// @Router /wms/units [get]
func (h *WmsUnitHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	// 状态筛选
	if status := ctx.DefaultQuery("status", ""); status != "" {
		params.AddFilter("status", status)
	}

	// 名称筛选
	if name := ctx.DefaultQuery("name", ""); name != "" {
		params.AddFilter("name_like", name)
	}

	result, err := h.wmsUnitService.List(ctx, &params)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
