package model

import (
	v1 "daisy-server/api/v1"

	"github.com/lib/pq"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// 文章表，用于管理文章信息
type CmsPost struct {
	gorm.Model
	Title    string                `gorm:"size:255; not null; index; comment:标题"`
	Slug     string                `gorm:"size:64; not null; index; comment:别名"`
	Summary  string                `gorm:"type:text; comment:描述"`
	Content  string                `gorm:"type:text; comment:内容"`
	Cover    string                `gorm:"size:255; comment:封面"`
	Author   string                `gorm:"size:64; not null; comment:作者"`
	From     string                `gorm:"size:64; not null; comment:来源"`
	Password string                `gorm:"size:64; not null; comment:密码"`
	Tags     datatypes.JSON        `gorm:"type:jsonb; comment:标签"`
	Files    []v1.UploadFileParams `gorm:"type:jsonb; serializer:json; comment:附件"`
	Order    int                   `gorm:"default:0; index; comment:排序"`
	Flag     pq.Int64Array         `gorm:"type:integer[]; comment:标志"`
	Status   bool                  `gorm:"default:false; index; comment:状态"`
	MetaId   uint                  `gorm:"default:0; index; comment:栏目ID"`
}

func (m *CmsPost) TableName() string {
	return "cms_post"
}
