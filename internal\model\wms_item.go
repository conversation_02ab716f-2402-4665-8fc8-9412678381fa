package model

import (
	"gorm.io/gorm"
)

// 物料表，用于管理物料信息
type WmsItem struct {
	gorm.Model
	Name      string `gorm:"size:255; not null; index; comment:名称"`
	Code      string `gorm:"size:64; not null; index; comment:编号"`
	Summary   string `gorm:"type:text; comment:备注"`
	Order     int    `gorm:"default:0; index; comment:排序"`
	Status    bool   `gorm:"default:false; index; comment:状态"`
	MetaId    uint   `gorm:"default:0; index; comment:分类ID"`
	TenantId  uint   `gorm:"default:0; index; comment:租户ID" copier:"-"`
	CreatedBy string `gorm:"size:64; comment:创建人" copier:"-"`
	UpdatedBy string `gorm:"size:64; comment:操作人" copier:"-"`
}

func (m *WmsItem) TableName() string {
	return "wms_item"
}
