package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/jwt"
	"net/http"

	"github.com/gin-gonic/gin"

	"go.uber.org/zap"
)

type AuthHandler struct {
	*Handler
	authService    service.AuthService
	sysMenuService service.SysMenuService
}

func NewAuthHandler(
	handler *Handler,
	authService service.AuthService,
	sysMenuService service.SysMenuService,
) *AuthHandler {
	return &AuthHandler{
		Handler:        handler,
		authService:    authService,
		sysMenuService: sysMenuService,
	}
}

// Login godoc
// @Summary 登录
// @Schemes
// @Description 使用用户名密码登录系统
// @Tags 认证模块
// @Accept json
// @Produce json
// @Param request body v1.AuthLoginParams true "登录参数"
// @Success 200 {object} v1.AuthLoginResponse
// @Router /auth/login [post]
func (h *Auth<PERSON>andler) Login(ctx *gin.Context) {
	var req v1.AuthLoginParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	token, refreshToken, err := h.authService.Login(ctx, &req)
	if err != nil {
		h.logger.WithContext(ctx).Error("authService.Login error", zap.Error(err))
		v1.HandleError(ctx, http.StatusUnauthorized, err, nil)
		return
	}

	v1.HandleSuccess(ctx, v1.AuthLoginData{
		Token:        token,
		RefreshToken: refreshToken,
	})
}

// Logout godoc
// @Summary 注销
// @Schemes
// @Description 注销登录
// @Tags 认证模块
// @Accept json
// @Produce json
// @Security Bearer
// @Success 200 {object} v1.Response
// @Router /auth/logout [post]
func (h *AuthHandler) Logout(ctx *gin.Context) {
	// 实际项目中可能需要进行令牌失效处理
	// 这里简单返回成功即可
	v1.HandleSuccess(ctx, nil)
}

// Refresh godoc
// @Summary 刷新令牌
// @Schemes
// @Description 使用 refreshToken 刷新认证令牌
// @Tags 认证模块
// @Accept json
// @Produce json
// @Param request body v1.AuthRefreshParams true "刷新参数"
// @Success 200 {object} v1.AuthLoginResponse
// @Router /auth/refresh [post]
func (h *AuthHandler) Refresh(ctx *gin.Context) {
	var req v1.AuthRefreshParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}
	token, refreshToken, err := h.authService.Refresh(ctx, req.RefreshToken)
	if err != nil {
		h.logger.WithContext(ctx).Error("authService.Refresh error", zap.Error(err))
		v1.HandleError(ctx, http.StatusUnauthorized, err, nil)
		return
	}
	v1.HandleSuccess(ctx, v1.AuthLoginData{
		Token:        token,
		RefreshToken: refreshToken,
	})
}

// Userinfo godoc
// @Summary 获取用户信息
// @Schemes
// @Description 获取当前登录用户的用户信息
// @Tags 认证模块
// @Accept json
// @Produce json
// @Security Bearer
// @Success 200 {object} v1.AuthUserinfoResponse
// @Router /auth/userinfo [get]
func (h *AuthHandler) Userinfo(ctx *gin.Context) {
	userId := jwt.GetUserId(ctx)
	if userId == "" {
		v1.HandleError(ctx, http.StatusUnauthorized, v1.ErrUnauthorized, nil)
		return
	}

	// 获取用户详情
	user, err := h.authService.Userinfo(ctx, userId)
	if err != nil {
		h.logger.WithContext(ctx).Error("authService.Userinfo error", zap.Error(err))
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, user)
}

// UpdateUserinfo godoc
// @Summary 更新用户信息
// @Schemes
// @Description 更新当前登录用户的用户信息
// @Tags 认证模块
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.AuthUserinfoUpdateParams true "更新参数"
// @Success 200 {object} v1.Response
// @Router /auth/userinfo [post]
func (h *AuthHandler) UpdateUserinfo(ctx *gin.Context) {
	userId := jwt.GetUserId(ctx)
	if userId == "" {
		v1.HandleError(ctx, http.StatusUnauthorized, v1.ErrUnauthorized, nil)
		return
	}

	var req v1.AuthUserinfoUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	// 更新用户信息
	if err := h.authService.UpdateUserinfo(ctx, userId, &req); err != nil {
		h.logger.WithContext(ctx).Error("authService.UpdateUserinfo error", zap.Error(err))
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// UpdatePassword godoc
// @Summary 修改用户密码
// @Schemes
// @Description 修改当前登录用户的密码
// @Tags 认证模块
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.AuthPasswordUpdateParams true "密码修改参数"
// @Success 200 {object} v1.Response
// @Router /auth/password [post]
func (h *AuthHandler) UpdatePassword(ctx *gin.Context) {
	userId := jwt.GetUserId(ctx)
	if userId == "" {
		v1.HandleError(ctx, http.StatusUnauthorized, v1.ErrUnauthorized, nil)
		return
	}

	var req v1.AuthPasswordUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	// 修改密码
	if err := h.authService.UpdatePassword(ctx, userId, &req); err != nil {
		h.logger.WithContext(ctx).Error("authService.UpdatePassword error", zap.Error(err))
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// ConstantRoutes godoc
// @Summary 获取常量路由
// @Schemes
// @Description 获取系统常量路由
// @Tags 认证模块
// @Accept json
// @Produce json
// @Success 200 {object} v1.Response
// @Router /auth/constant_routes [get]
func (h *AuthHandler) ConstantRoutes(ctx *gin.Context) {
	menus, err := h.sysMenuService.GetConstantRoutes(ctx)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	var routes []map[string]interface{}
	for _, menu := range menus {
		route := map[string]interface{}{
			"name":      menu.RouteName,
			"path":      menu.RoutePath,
			"component": "layout." + menu.Layout + "$view." + menu.Component,
			"meta": map[string]interface{}{
				"title":      menu.MenuName,
				"constant":   menu.Constant,
				"hideInMenu": menu.HideInMenu,
			},
		}
		routes = append(routes, route)
	}

	v1.HandleSuccess(ctx, routes)
}

// ExistRoute godoc
// @Summary 检查路由是否存在
// @Schemes
// @Description 检查指定路由名称是否存在
// @Tags 认证模块
// @Accept json
// @Produce json
// @Param routeName query string true "路由名称"
// @Success 200 {object} v1.Response
// @Router /auth/exist_route [get]
func (h *AuthHandler) ExistRoute(ctx *gin.Context) {
	routeName := ctx.Query("routeName")
	if routeName == "" {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	exists, err := h.sysMenuService.CheckRouteExists(ctx, routeName)
	if err != nil {
		h.logger.WithContext(ctx).Error("sysMenuService.CheckRouteExists error", zap.Error(err))
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, exists)
}

// UserRoutes godoc
// @Summary 获取用户路由
// @Schemes
// @Description 获取当前登录用户的菜单路由
// @Tags 认证模块
// @Accept json
// @Produce json
// @Security Bearer
// @Success 200 {object} v1.Response
// @Router /auth/user_routes [get]
func (h *AuthHandler) UserRoutes(ctx *gin.Context) {
	userId := jwt.GetUserId(ctx)
	if userId == "" {
		v1.HandleError(ctx, http.StatusUnauthorized, v1.ErrUnauthorized, nil)
		return
	}

	// 获取用户信息
	user, err := h.authService.Userinfo(ctx, userId)
	if err != nil {
		h.logger.WithContext(ctx).Error("authService.Userinfo error", zap.Error(err))
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	// 收集所有角色的菜单ID
	var allMenuIds []int64
	isSuper := false

	for _, role := range user.Roles {
		// 检查是否是超级管理员
		if role.Code == "super" {
			isSuper = true
			break
		}
		// 收集菜单ID
		for _, menuId := range role.MenuIds {
			allMenuIds = append(allMenuIds, menuId)
		}
	}

	// 获取用户路由
	menus, err := h.sysMenuService.GetUserRoutes(ctx, allMenuIds, isSuper)
	if err != nil {
		h.logger.WithContext(ctx).Error("sysMenuService.GetUserRoutes error", zap.Error(err))
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	var routes []map[string]interface{}
	for _, menu := range menus {
		// 根据 menu_type 构建 component
		var component string
		// 如果 layout 和 component 都不为空，则组合使用
		if menu.Layout != "default" && menu.Component != "" {
			component = "layout." + menu.Layout + "$view." + menu.Component
		} else if menu.Layout != "default" {
			// 如果只有 layout 不为空
			component = "layout." + menu.Layout
		} else if menu.Component != "" {
			// 如果只有 component 不为空
			component = "view." + menu.Component
		}

		route := map[string]interface{}{
			"id":        menu.ID,
			"name":      menu.RouteName,
			"path":      menu.RoutePath,
			"component": component,
			"meta": map[string]interface{}{
				"title":      menu.MenuName,
				"icon":       menu.Icon,
				"order":      menu.Order,
				"keepAlive":  menu.KeepAlive,
				"hideInMenu": menu.HideInMenu,
				"multiTab":   menu.MultiTab,
				"href":       menu.Href,
			},
			"parentId": menu.ParentId,
		}
		routes = append(routes, route)
	}

	v1.HandleSuccess(ctx, routes)
}
