package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type SysConfigRepository interface {
	Create(ctx context.Context, config *model.SysConfig) error
	Update(ctx context.Context, config *model.SysConfig) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.SysConfig, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.SysConfig, int64, error)
}

func NewSysConfigRepository(
	repository *Repository,
) SysConfigRepository {
	return &sysConfigRepository{
		Repository: repository,
	}
}

type sysConfigRepository struct {
	*Repository
}

func (r *sysConfigRepository) Create(ctx context.Context, config *model.SysConfig) error {
	if err := r.DB(ctx).Create(config).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysConfigRepository) Update(ctx context.Context, config *model.SysConfig) error {
	if err := r.DB(ctx).Save(config).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysConfigRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.SysConfig{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysConfigRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.SysConfig{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysConfigRepository) Get(ctx context.Context, id uint) (*model.SysConfig, error) {
	var config model.SysConfig
	if err := r.DB(ctx).First(&config, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &config, nil
}

func (r *sysConfigRepository) List(ctx context.Context, params *pagination.Params) ([]*model.SysConfig, int64, error) {
	var records []*model.SysConfig
	var total int64

	db := r.DB(ctx).Model(&model.SysConfig{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.SysConfig{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
