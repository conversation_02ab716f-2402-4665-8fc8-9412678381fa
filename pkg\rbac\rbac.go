package rbac

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/casbin/casbin/v2"
	casbinmodel "github.com/casbin/casbin/v2/model"
	"github.com/casbin/casbin/v2/persist"
	rediswatcher "github.com/casbin/redis-watcher/v2"
	"github.com/redis/go-redis/v9"
	"github.com/spf13/viper"
	"go.uber.org/zap"

	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/log"
)

// RBAC 结构体，用于实现RBAC权限控制
type RBAC struct {
	enforcer    *casbin.Enforcer
	repository  *repository.Repository
	logger      *log.Logger
	watcher     persist.Watcher
	conf        *viper.Viper
	redisClient *redis.Client
}

// NewRBAC 创建RBAC实例
func NewRBAC(conf *viper.Viper, repository *repository.Repository, logger *log.Logger, redisClient *redis.Client) (*RBAC, error) {
	var enforcer *casbin.Enforcer
	var err error

	// 检查是否存在新的配置方式
	if conf.IsSet("security.rbac.model") {
		// 使用配置文件中的RBAC模型
		m := casbinmodel.NewModel()

		// 请求定义
		if conf.IsSet("security.rbac.model.request_definition.r") {
			reqDef := conf.GetStringSlice("security.rbac.model.request_definition.r")
			m.AddDef("r", "r", strings.Join(reqDef, ", "))
		} else {
			return nil, fmt.Errorf("RBAC模型配置缺少request_definition")
		}

		// 策略定义
		if conf.IsSet("security.rbac.model.policy_definition.p") {
			policyDef := conf.GetStringSlice("security.rbac.model.policy_definition.p")
			m.AddDef("p", "p", strings.Join(policyDef, ", "))
		} else {
			return nil, fmt.Errorf("RBAC模型配置缺少policy_definition")
		}

		// 角色定义
		if conf.IsSet("security.rbac.model.role_definition.g") {
			roleDef := conf.GetStringSlice("security.rbac.model.role_definition.g")
			m.AddDef("g", "g", strings.Join(roleDef, ", "))
		} else {
			return nil, fmt.Errorf("RBAC模型配置缺少role_definition")
		}

		// 策略效果
		if conf.IsSet("security.rbac.model.policy_effect.e") {
			policyEffect := conf.GetString("security.rbac.model.policy_effect.e")
			m.AddDef("e", "e", policyEffect)
		} else {
			return nil, fmt.Errorf("RBAC模型配置缺少policy_effect")
		}

		// 匹配器
		if conf.IsSet("security.rbac.model.matchers.m") {
			matcher := conf.GetString("security.rbac.model.matchers.m")
			m.AddDef("m", "m", matcher)
		} else {
			return nil, fmt.Errorf("RBAC模型配置缺少matchers")
		}

		// 初始化Enforcer
		enforcer, err = casbin.NewEnforcer(m)
		if err != nil {
			return nil, fmt.Errorf("初始化Casbin Enforcer失败: %w", err)
		}

		// 从配置加载策略
		if conf.IsSet("security.rbac.policy") {
			policies := conf.Get("security.rbac.policy")
			if policies != nil {
				policyList, ok := policies.([]interface{})
				if !ok {
					return nil, fmt.Errorf("RBAC策略配置格式错误")
				}

				for _, p := range policyList {
					policy, ok := p.([]interface{})
					if !ok {
						continue
					}

					// 将策略转换为字符串切片
					strPolicy := make([]string, 0, len(policy))
					for _, item := range policy {
						strItem, ok := item.(string)
						if !ok {
							strItem = fmt.Sprintf("%v", item)
						}
						strPolicy = append(strPolicy, strItem)
					}

					// 添加策略
					if len(strPolicy) >= 2 {
						if _, err := enforcer.AddPolicy(strPolicy); err != nil {
							return nil, fmt.Errorf("添加策略失败: %w", err)
						}
					}
				}
			}
		}
	} else {
		// 使用传统方式从文件加载
		enforcer, err = casbin.NewEnforcer("config/rbac_model.conf", "config/rbac_policy.csv")
		if err != nil {
			return nil, fmt.Errorf("初始化Casbin Enforcer失败: %w", err)
		}
	}

	// 初始化Redis Watcher
	watcher, err := rediswatcher.NewWatcher(conf.GetString("data.redis.addr"), rediswatcher.WatcherOptions{
		Options: redis.Options{
			Password: conf.GetString("data.redis.password"),
			DB:       conf.GetInt("data.redis.db"),
		},
		Channel: "/casbin",
	})
	if err != nil {
		return nil, fmt.Errorf("初始化Redis Watcher失败: %w", err)
	}

	// 设置回调函数，当策略变更时更新enforcer
	err = watcher.SetUpdateCallback(func(s string) {
		logger.Info("Casbin策略已更新", zap.String("data", s))
		_ = enforcer.LoadPolicy()
	})
	if err != nil {
		return nil, fmt.Errorf("设置Watcher回调函数失败: %w", err)
	}

	// 将watcher设置到enforcer
	err = enforcer.SetWatcher(watcher)
	if err != nil {
		return nil, fmt.Errorf("设置Watcher到Enforcer失败: %w", err)
	}

	rbacInstance := &RBAC{
		enforcer:    enforcer,
		repository:  repository,
		logger:      logger,
		watcher:     watcher,
		conf:        conf,
		redisClient: redisClient,
	}

	// 初始化API缓存
	if err := rbacInstance.refreshApiCache(context.Background()); err != nil {
		logger.Error("初始化API缓存失败", zap.Error(err))
	}

	return rbacInstance, nil
}

// 刷新API缓存
func (r *RBAC) refreshApiCache(ctx context.Context) error {
	// 获取所有API
	var apis []model.SysApi
	err := r.repository.DB(ctx).Find(&apis).Error
	if err != nil {
		return fmt.Errorf("获取API信息失败: %w", err)
	}

	// 使用管道批量操作以提高性能
	pipe := r.redisClient.Pipeline()

	// 先清除旧缓存
	pipe.Del(ctx, "rbac:apis:all")

	// 为每个API创建缓存
	for _, api := range apis {
		apiJson, err := json.Marshal(api)
		if err != nil {
			r.logger.Error("序列化API失败", zap.Error(err), zap.Uint("apiId", api.ID))
			continue
		}

		// 按ID存储API
		pipe.HSet(ctx, "rbac:apis:all", fmt.Sprintf("%d", api.ID), apiJson)
	}

	// 执行管道命令
	_, err = pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("缓存API信息失败: %w", err)
	}

	// 设置缓存过期时间（可选，根据需求配置）
	r.redisClient.Expire(ctx, "rbac:apis:all", 24*time.Hour)

	r.logger.Info("API缓存刷新成功", zap.Int("apiCount", len(apis)))
	return nil
}

// 从缓存获取API信息
func (r *RBAC) getApiFromCache(ctx context.Context, apiIds []uint) (map[uint]model.SysApi, error) {
	apiMap := make(map[uint]model.SysApi)

	// 如果没有API ID，直接返回空映射
	if len(apiIds) == 0 {
		return apiMap, nil
	}

	// 准备要查询的API ID列表
	apiIdStrings := make([]string, len(apiIds))
	for i, id := range apiIds {
		apiIdStrings[i] = fmt.Sprintf("%d", id)
	}

	// 从Redis批量获取API信息
	apiJsonMap, err := r.redisClient.HMGet(ctx, "rbac:apis:all", apiIdStrings...).Result()
	if err != nil && err != redis.Nil {
		return nil, fmt.Errorf("从缓存获取API信息失败: %w", err)
	}

	// 处理缓存未命中的情况
	missingApiIds := make([]uint, 0)

	// 解析获取到的API信息
	for i, apiJson := range apiJsonMap {
		if apiJson == nil {
			// 缓存未命中，记录需要从数据库查询的ID
			missingApiIds = append(missingApiIds, apiIds[i])
			continue
		}

		// 解析JSON
		var api model.SysApi
		if err := json.Unmarshal([]byte(apiJson.(string)), &api); err != nil {
			r.logger.Error("解析API缓存失败", zap.Error(err), zap.String("apiId", apiIdStrings[i]))
			missingApiIds = append(missingApiIds, apiIds[i])
			continue
		}

		apiMap[api.ID] = api
	}

	// 如果有缓存未命中的API，从数据库查询
	if len(missingApiIds) > 0 {
		var missingApis []model.SysApi
		err := r.repository.DB(ctx).Where("id IN ?", missingApiIds).Find(&missingApis).Error
		if err != nil {
			return apiMap, fmt.Errorf("获取缺失的API信息失败: %w", err)
		}

		// 添加到结果映射
		for _, api := range missingApis {
			apiMap[api.ID] = api

			// 更新缓存
			apiJson, err := json.Marshal(api)
			if err == nil {
				r.redisClient.HSet(ctx, "rbac:apis:all", fmt.Sprintf("%d", api.ID), apiJson)
			}
		}
	}

	return apiMap, nil
}

// CheckPermission 检查用户是否有权限访问指定路径，并返回用户信息
func (r *RBAC) CheckPermission(ctx context.Context, userId string, reqPath, reqMethod string) (bool, *model.SysUser, error) {
	var user model.SysUser
	var roles []model.SysRole
	var apiMap = make(map[uint]model.SysApi)

	// 去掉/v1前缀，以匹配数据库中的API路径
	if len(reqPath) > 3 && reqPath[:3] == "/v1" {
		reqPath = reqPath[3:]
	}

	// 查询用户信息
	err := r.repository.DB(ctx).Where("user_id = ?", userId).First(&user).Error
	if err != nil {
		return false, nil, fmt.Errorf("获取用户信息失败: %w", err)
	}

	// 如果用户没有角色，直接返回无权限
	if len(user.RoleIds) == 0 {
		return false, &user, nil
	}

	// 提取角色ID为普通的int64切片
	var roleIds []int64
	for _, id := range user.RoleIds {
		roleIds = append(roleIds, id)
	}

	// 查询用户角色
	err = r.repository.DB(ctx).Where("id IN ?", roleIds).Find(&roles).Error
	if err != nil {
		return false, &user, fmt.Errorf("获取用户角色失败: %w", err)
	}

	// 检查是否有super角色
	for _, role := range roles {
		if role.Code == "super" {
			// super角色直接放行
			r.logger.Info("super角色用户访问",
				zap.String("userId", userId),
				zap.String("path", reqPath),
				zap.String("method", reqMethod))
			return true, &user, nil
		}
	}

	// 收集所有角色的API IDs（使用map去重）
	apiIdsMap := make(map[uint]struct{})
	for _, role := range roles {
		for _, apiId := range role.ApiIds {
			apiIdsMap[uint(apiId)] = struct{}{}
		}
	}

	// 将map转换为切片
	var allApiIds []uint
	for apiId := range apiIdsMap {
		allApiIds = append(allApiIds, apiId)
	}

	// 如果用户没有任何API权限
	if len(allApiIds) == 0 {
		return false, &user, nil
	}

	// 从缓存获取API信息
	apiMap, err = r.getApiFromCache(ctx, allApiIds)
	if err != nil {
		r.logger.Error("从缓存获取API失败，回退到数据库查询", zap.Error(err))

		// 缓存获取失败时回退到数据库查询
		var apis []model.SysApi
		err = r.repository.DB(ctx).Where("id IN ?", allApiIds).Find(&apis).Error
		if err != nil {
			return false, &user, fmt.Errorf("获取API信息失败: %w", err)
		}

		// 构建API映射
		for _, api := range apis {
			apiMap[api.ID] = api
		}
	}

	// 检查是否有权限访问当前路径
	for _, apiId := range allApiIds {
		api, exists := apiMap[apiId]
		if !exists {
			continue
		}

		// 检查方法是否匹配
		if api.Method != reqMethod && api.Method != "*" {
			continue
		}

		// 检查路径是否匹配（支持路径参数）
		if pathMatch(reqPath, api.Path) {
			return true, &user, nil
		}
	}

	return false, &user, nil
}

// pathMatch 检查请求路径是否匹配API路径
// 支持路径参数匹配，如 /api/users/{id} 可以匹配 /api/users/123
func pathMatch(reqPath, apiPath string) bool {
	// 如果完全相同，直接返回true
	if reqPath == apiPath {
		return true
	}

	// 处理路径参数
	// 将API路径中的 {xxx} 替换为正则表达式 ([^/]+)
	regexPattern := regexp.MustCompile(`\{[^/]+\}`)
	pattern := regexPattern.ReplaceAllString(apiPath, `([^/]+)`)

	// 确保完全匹配
	pattern = "^" + pattern + "$"

	// 编译正则表达式
	regex, err := regexp.Compile(pattern)
	if err != nil {
		return false
	}

	// 匹配路径
	return regex.MatchString(reqPath)
}

// Close 关闭Watcher连接
func (r *RBAC) Close() {
	if r.watcher != nil {
		r.watcher.Close()
	}
}
