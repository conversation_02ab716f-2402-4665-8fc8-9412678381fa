package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type WmsLogRepository interface {
	Create(ctx context.Context, log *model.WmsLog) error
	Update(ctx context.Context, log *model.WmsLog) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.WmsLog, error)
	GetByIds(ctx context.Context, ids []uint) ([]*model.WmsLog, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.WmsLog, int64, error)
}

func NewWmsLogRepository(
	repository *Repository,
) WmsLogRepository {
	return &wmsLogRepository{
		Repository: repository,
	}
}

type wmsLogRepository struct {
	*Repository
}

func (r *wmsLogRepository) Create(ctx context.Context, log *model.WmsLog) error {
	if err := r.DB(ctx).Create(log).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsLogRepository) Update(ctx context.Context, log *model.WmsLog) error {
	if err := r.DB(ctx).Save(log).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsLogRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.WmsLog{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsLogRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.WmsLog{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsLogRepository) Get(ctx context.Context, id uint) (*model.WmsLog, error) {
	var log model.WmsLog
	if err := r.DB(ctx).First(&log, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &log, nil
}

func (r *wmsLogRepository) GetByIds(ctx context.Context, ids []uint) ([]*model.WmsLog, error) {
	var logs []*model.WmsLog
	if len(ids) == 0 {
		return logs, nil
	}
	
	if err := r.DB(ctx).Where("id IN ?", ids).Find(&logs).Error; err != nil {
		return nil, err
	}
	return logs, nil
}

func (r *wmsLogRepository) List(ctx context.Context, params *pagination.Params) ([]*model.WmsLog, int64, error) {
	var records []*model.WmsLog
	var total int64

	db := r.DB(ctx).Model(&model.WmsLog{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.WmsLog{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
