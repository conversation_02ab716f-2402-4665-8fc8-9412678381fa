package v1

import "github.com/lib/pq"

type WmsMetaCreateParams struct {
	Name       string        `json:"name" binding:"required,max=64" example:"电子产品"`
	Order      int           `json:"order" example:"1"`
	Status     bool          `json:"status" example:"true"`
	ParentId   uint          `json:"parentId" example:"0"`
	ParentPath pq.Int64Array `json:"parentPath" swaggertype:"array,integer" example:"1,2,3"`
	TenantId   uint          `json:"tenantId" example:"1"`
	CreatedBy  string        `json:"createdBy" example:"管理员"`
	UpdatedBy  string        `json:"updatedBy" example:"管理员"`
}

type WmsMetaUpdateParams struct {
	WmsMetaCreateParams
}

type WmsMetaResponse struct {
	ID         uint          `json:"id"`
	Name       string        `json:"name"`
	Order      int           `json:"order"`
	Status     bool          `json:"status"`
	ParentId   uint          `json:"parentId"`
	ParentPath pq.Int64Array `json:"parentPath"`
	TenantId   uint          `json:"tenantId"`
	CreatedBy  string        `json:"createdBy"`
	UpdatedBy  string        `json:"updatedBy"`
	CreatedAt  string        `json:"createdAt"`
	UpdatedAt  string        `json:"updatedAt"`
}
