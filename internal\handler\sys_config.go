package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type SysConfigHandler struct {
	*Handler
	sysConfigService service.SysConfigService
}

func NewSysConfigHandler(
	handler *Handler,
	sysConfigService service.SysConfigService,
) *SysConfigHandler {
	return &SysConfigHandler{
		Handler:          handler,
		sysConfigService: sysConfigService,
	}
}

// Create godoc
// @Summary 创建配置
// @Schemes
// @Description 创建新的配置记录
// @Tags 系统模块,配置管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.SysConfigCreateParams true "配置信息"
// @Success 200 {object} v1.Response
// @Router /system/configs [post]
func (h *SysConfigHandler) Create(ctx *gin.Context) {
	var req v1.SysConfigCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysConfigService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新配置
// @Schemes
// @Description 更新指定ID的配置信息
// @Tags 系统模块,配置管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "配置ID"
// @Param request body v1.SysConfigUpdateParams true "配置信息"
// @Success 200 {object} v1.Response
// @Router /system/configs/{id} [patch]
func (h *SysConfigHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.SysConfigUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysConfigService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除配置
// @Schemes
// @Description 删除指定ID的配置
// @Tags 系统模块,配置管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "配置ID"
// @Success 200 {object} v1.Response
// @Router /system/configs/{id} [delete]
func (h *SysConfigHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysConfigService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除配置
// @Schemes
// @Description 批量删除指定IDs的配置
// @Tags 系统模块,配置管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "配置IDs"
// @Success 200 {object} v1.Response
// @Router /system/configs [delete]
func (h *SysConfigHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysConfigService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取配置
// @Schemes
// @Description 获取指定ID的配置信息
// @Tags 系统模块,配置管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "配置ID"
// @Success 200 {object} v1.Response{data=v1.SysConfigResponse}
// @Router /system/configs/{id} [get]
func (h *SysConfigHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	config, err := h.sysConfigService.Get(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, config)
}

// List godoc
// @Summary 获取配置列表
// @Schemes
// @Description 分页获取配置列表
// @Tags 系统模块,配置管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param status query bool false "状态筛选" example:"true"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.SysConfigResponse}}
// @Router /system/configs [get]
func (h *SysConfigHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	result, err := h.sysConfigService.List(ctx, &params)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
