package model

import (
	"github.com/lib/pq"
	"gorm.io/gorm"
)

// 用户表，用于管理用户信息
type SysUser struct {
	gorm.Model
	UserId   string        `gorm:"size:64; not null; unique; comment:用户ID"`
	Username string        `gorm:"size:64; not null; index; comment:用户名"`
	Password string        `gorm:"size:128; not null; comment:密码" copier:"-"`
	Nickname string        `gorm:"size:64; not null; comment:昵称"`
	Gender   uint          `gorm:"default:0; index; comment:性别"`
	Phone    string        `gorm:"size:20; index; comment:手机号"`
	Email    string        `gorm:"size:128; not null; comment:邮箱"`
	Avatar   string        `gorm:"size:255; comment:头像"`
	Status   bool          `gorm:"default:false; index; comment:状态"`
	RoleIds  pq.Int64Array `gorm:"type:integer[]; comment:角色IDs"`
	TenantId uint          `gorm:"default:0; index; comment:租户ID"`
}

func (m *SysUser) TableName() string {
	return "sys_user"
}
