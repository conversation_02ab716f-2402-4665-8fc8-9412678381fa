package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type SysTenantRepository interface {
	Create(ctx context.Context, tenant *model.SysTenant) error
	Update(ctx context.Context, tenant *model.SysTenant) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.SysTenant, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.SysTenant, int64, error)
}

func NewSysTenantRepository(
	repository *Repository,
) SysTenantRepository {
	return &sysTenantRepository{
		Repository: repository,
	}
}

type sysTenantRepository struct {
	*Repository
}

func (r *sysTenantRepository) Create(ctx context.Context, tenant *model.SysTenant) error {
	if err := r.DB(ctx).Create(tenant).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysTenantRepository) Update(ctx context.Context, tenant *model.SysTenant) error {
	if err := r.DB(ctx).Save(tenant).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysTenantRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.SysTenant{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysTenantRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.SysTenant{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysTenantRepository) Get(ctx context.Context, id uint) (*model.SysTenant, error) {
	var tenant model.SysTenant
	if err := r.DB(ctx).First(&tenant, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &tenant, nil
}

func (r *sysTenantRepository) List(ctx context.Context, params *pagination.Params) ([]*model.SysTenant, int64, error) {
	var records []*model.SysTenant
	var total int64

	db := r.DB(ctx).Model(&model.SysTenant{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.SysTenant{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
