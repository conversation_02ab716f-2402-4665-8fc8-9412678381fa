package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/jinzhu/copier"
)

type CmsPostService interface {
	Create(ctx context.Context, req *v1.CmsPostCreateParams) error
	Update(ctx context.Context, id uint, req *v1.CmsPostUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.CmsPostResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewCmsPostService(
	service *Service,
	cmsPostRepository repository.CmsPostRepository,
) CmsPostService {
	return &cmsPostService{
		Service:           service,
		cmsPostRepository: cmsPostRepository,
	}
}

type cmsPostService struct {
	*Service
	cmsPostRepository repository.CmsPostRepository
}

// 文章相关方法实现
func (s *cmsPostService) Create(ctx context.Context, req *v1.CmsPostCreateParams) error {
	post := &model.CmsPost{}
	if err := copier.Copy(post, req); err != nil {
		return err
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.cmsPostRepository.Create(ctx, post); err != nil {
			return err
		}
		return nil
	})
}

func (s *cmsPostService) Update(ctx context.Context, id uint, req *v1.CmsPostUpdateParams) error {
	post, err := s.cmsPostRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	if err := copier.Copy(post, req); err != nil {
		return err
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.cmsPostRepository.Update(ctx, post); err != nil {
			return err
		}
		return nil
	})
}

func (s *cmsPostService) Delete(ctx context.Context, id uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.cmsPostRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *cmsPostService) BatchDelete(ctx context.Context, ids []uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.cmsPostRepository.BatchDelete(ctx, ids); err != nil {
			return err
		}
		return nil
	})
}

func (s *cmsPostService) Get(ctx context.Context, id uint) (*v1.CmsPostResponse, error) {
	post, err := s.cmsPostRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	response := &v1.CmsPostResponse{}
	if err := copier.Copy(response, post); err != nil {
		return nil, err
	}

	response.CreatedAt = post.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = post.UpdatedAt.Format(time.RFC3339)

	return response, nil
}

func (s *cmsPostService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	posts, total, err := s.cmsPostRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.CmsPostResponse, 0, len(posts))
	for _, post := range posts {
		response := &v1.CmsPostResponse{}
		if err := copier.Copy(response, post); err != nil {
			return nil, err
		}

		response.CreatedAt = post.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = post.UpdatedAt.Format(time.RFC3339)

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
