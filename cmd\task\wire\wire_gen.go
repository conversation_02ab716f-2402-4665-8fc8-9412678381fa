// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"daisy-server/internal/repository"
	"daisy-server/internal/server"
	"daisy-server/internal/task"
	"daisy-server/pkg/app"
	"daisy-server/pkg/log"
	"daisy-server/pkg/sid"
	"github.com/google/wire"
	"github.com/spf13/viper"
)

// Injectors from wire.go:

func NewWire(viperViper *viper.Viper, logger *log.Logger) (*app.App, func(), error) {
	db := repository.NewDB(viperViper, logger)
	repositoryRepository := repository.NewRepository(logger, db)
	transaction := repository.NewTransaction(repositoryRepository)
	sidSid := sid.NewSid()
	taskTask := task.NewTask(transaction, logger, sidSid)
	sysUserRepository := repository.NewSysUserRepository(repositoryRepository)
	userTask := task.NewUserTask(taskTask, sysUserRepository)
	taskServer := server.NewTaskServer(logger, userTask)
	appApp := newApp(taskServer)
	return appApp, func() {
	}, nil
}

// wire.go:

var repositorySet = wire.NewSet(repository.NewDB, repository.NewRepository, repository.NewTransaction, repository.NewSysUserRepository)

var taskSet = wire.NewSet(task.NewTask, task.NewUserTask)

var serverSet = wire.NewSet(server.NewTaskServer)

// build App
func newApp(task2 *server.TaskServer,
) *app.App {
	return app.NewApp(app.WithServer(task2), app.WithName("demo-task"))
}
