package handler

import (
	"context"
	"mime/multipart"
	"net/http"

	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"

	"github.com/gin-gonic/gin"
)

type UploadHandler struct {
	*Handler
	uploadService service.UploadService
}

func NewUploadHandler(handler *Handler, uploadService service.UploadService) *UploadHandler {
	return &UploadHandler{
		Handler:       handler,
		uploadService: uploadService,
	}
}

// UploadImage 上传图片
// @Summary 上传图片
// @Description 上传图片到MinIO服务器
// @Tags 上传模块
// @Accept multipart/form-data
// @Produce json
// @Security Bearer
// @Param file formData file true "图片文件"
// @Success 200 {object} v1.Response
// @Router /upload/image [post]
func (h *UploadHandler) UploadImage(ctx *gin.Context) {
	h.uploader(ctx, h.uploadService.UploadImage)
}

// UploadFile 上传文件
// @Summary 上传文件
// @Description 上传文件到MinIO服务器
// @Tags 上传模块
// @Accept multipart/form-data
// @Produce json
// @Security Bearer
// @Param file formData file true "文件"
// @Success 200 {object} v1.Response
// @Router /upload/file [post]
func (h *UploadHandler) UploadFile(ctx *gin.Context) {
	h.uploader(ctx, h.uploadService.UploadFile)
}

func (h *UploadHandler) uploader(ctx *gin.Context, uploadFn func(c context.Context, file *multipart.FileHeader) (string, string, error)) {
	// 获取上传的文件
	file, err := ctx.FormFile("file")
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	// 调用service层上传文件
	fileURL, fileName, err := uploadFn(ctx.Request.Context(), file)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, gin.H{
		"url":      fileURL,
		"fileName": fileName,
	})
}

// UploadRemove 删除资源
// @Summary 删除资源
// @Description 从MinIO服务器删除资源
// @Tags 上传模块
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.UploadRemoveParams true "文件信息"
// @Success 200 {object} v1.Response
// @Router /upload/remove [delete]
func (h *UploadHandler) UploadRemove(ctx *gin.Context) {
	var req v1.UploadRemoveParams

	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	// 调用service层删除文件
	err := h.uploadService.RemoveFile(ctx.Request.Context(), req.FileName)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}
