package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type SysMenuRepository interface {
	Create(ctx context.Context, menu *model.SysMenu) error
	Update(ctx context.Context, menu *model.SysMenu) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.SysMenu, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.SysMenu, int64, error)
	GetConstantRoutes(ctx context.Context) ([]*model.SysMenu, error)
	CheckRouteExists(ctx context.Context, routeName string) (bool, error)
	GetUserRoutes(ctx context.Context, menuIds []int64, isSuper bool) ([]*model.SysMenu, error)
}

func NewSysMenuRepository(
	repository *Repository,
) SysMenuRepository {
	return &sysMenuRepository{
		Repository: repository,
	}
}

type sysMenuRepository struct {
	*Repository
}

func (r *sysMenuRepository) Create(ctx context.Context, menu *model.SysMenu) error {
	if err := r.DB(ctx).Create(menu).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysMenuRepository) Update(ctx context.Context, menu *model.SysMenu) error {
	if err := r.DB(ctx).Save(menu).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysMenuRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.SysMenu{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysMenuRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.SysMenu{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysMenuRepository) Get(ctx context.Context, id uint) (*model.SysMenu, error) {
	var menu model.SysMenu
	if err := r.DB(ctx).First(&menu, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &menu, nil
}

func (r *sysMenuRepository) List(ctx context.Context, params *pagination.Params) ([]*model.SysMenu, int64, error) {
	var records []*model.SysMenu
	var total int64

	db := r.DB(ctx).Model(&model.SysMenu{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.SysMenu{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}

func (r *sysMenuRepository) GetConstantRoutes(ctx context.Context) ([]*model.SysMenu, error) {
	var menus []*model.SysMenu
	err := r.DB(ctx).Where("constant = ? AND status = ?", true, true).Order("\"order\" ASC").Find(&menus).Error
	if err != nil {
		return nil, err
	}
	return menus, nil
}

func (r *sysMenuRepository) CheckRouteExists(ctx context.Context, routeName string) (bool, error) {
	var count int64
	err := r.DB(ctx).Model(&model.SysMenu{}).Where("route_name = ? AND status = ?", routeName, true).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func (r *sysMenuRepository) GetUserRoutes(ctx context.Context, menuIds []int64, isSuper bool) ([]*model.SysMenu, error) {
	var menus []*model.SysMenu
	db := r.DB(ctx).Where("constant = ? AND status = ?", false, true)

	if !isSuper && len(menuIds) > 0 {
		db = db.Where("id IN ?", menuIds)
	}

	err := db.Order("parent_id ASC, \"order\" ASC").Find(&menus).Error
	if err != nil {
		return nil, err
	}
	return menus, nil
}
