package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type WmsSkuRepository interface {
	Create(ctx context.Context, sku *model.WmsSku) error
	Update(ctx context.Context, sku *model.WmsSku) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.WmsSku, error)
	GetByIds(ctx context.Context, ids []uint) ([]*model.WmsSku, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.WmsSku, int64, error)
}

func NewWmsSkuRepository(
	repository *Repository,
) WmsSkuRepository {
	return &wmsSkuRepository{
		Repository: repository,
	}
}

type wmsSkuRepository struct {
	*Repository
}

func (r *wmsSkuRepository) Create(ctx context.Context, sku *model.WmsSku) error {
	if err := r.DB(ctx).Create(sku).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsSkuRepository) Update(ctx context.Context, sku *model.WmsSku) error {
	if err := r.DB(ctx).Save(sku).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsSkuRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.WmsSku{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsSkuRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.WmsSku{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsSkuRepository) Get(ctx context.Context, id uint) (*model.WmsSku, error) {
	var sku model.WmsSku
	if err := r.DB(ctx).First(&sku, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &sku, nil
}

func (r *wmsSkuRepository) GetByIds(ctx context.Context, ids []uint) ([]*model.WmsSku, error) {
	var skus []*model.WmsSku
	if len(ids) == 0 {
		return skus, nil
	}
	
	if err := r.DB(ctx).Where("id IN ?", ids).Find(&skus).Error; err != nil {
		return nil, err
	}
	return skus, nil
}

func (r *wmsSkuRepository) List(ctx context.Context, params *pagination.Params) ([]*model.WmsSku, int64, error) {
	var records []*model.WmsSku
	var total int64

	db := r.DB(ctx).Model(&model.WmsSku{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.WmsSku{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
