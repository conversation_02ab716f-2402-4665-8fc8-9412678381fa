package model

import (
	v1 "daisy-server/api/v1"

	"gorm.io/gorm"
)

// 系统配置表，用于管理系统的配置信息
type SysConfig struct {
	gorm.Model
	Name    string              `gorm:"size:64; not null; unique; comment:名称"`
	Code    string              `gorm:"size:64; not null; unique; comment:编码"`
	Summary string              `gorm:"type:text; comment:描述"`
	Status  bool                `gorm:"default:false; index; comment:状态"`
	Params  []v1.SysConfigParam `gorm:"type:jsonb; serializer:json; comment:参数"`
}

func (m *SysConfig) TableName() string {
	return "sys_config"
}
