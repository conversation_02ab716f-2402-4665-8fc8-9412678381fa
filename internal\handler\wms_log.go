package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

type WmsLogHandler struct {
	*Handler
	wmsLogService service.WmsLogService
}

func NewWmsLogHandler(
	handler *Handler,
	wmsLogService service.WmsLogService,
) *WmsLogHandler {
	return &WmsLogHandler{
		Handler:       handler,
		wmsLogService: wmsLogService,
	}
}

// Create godoc
// @Summary 创建库存日志
// @Schemes
// @Description 创建新的库存日志记录
// @Tags 仓储模块,库存日志管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.WmsLogCreateParams true "库存日志信息"
// @Success 200 {object} v1.Response
// @Router /wms/logs [post]
func (h *WmsLogHandler) Create(ctx *gin.Context) {
	var req v1.WmsLogCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsLogService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新库存日志
// @Schemes
// @Description 更新指定ID的库存日志信息
// @Tags 仓储模块,库存日志管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "库存日志ID"
// @Param request body v1.WmsLogUpdateParams true "库存日志信息"
// @Success 200 {object} v1.Response
// @Router /wms/logs/{id} [patch]
func (h *WmsLogHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.WmsLogUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsLogService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除库存日志
// @Schemes
// @Description 删除指定ID的库存日志
// @Tags 仓储模块,库存日志管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "库存日志ID"
// @Success 200 {object} v1.Response
// @Router /wms/logs/{id} [delete]
func (h *WmsLogHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsLogService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除库存日志
// @Schemes
// @Description 批量删除指定IDs的库存日志
// @Tags 仓储模块,库存日志管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "库存日志IDs"
// @Success 200 {object} v1.Response
// @Router /wms/logs [delete]
func (h *WmsLogHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsLogService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取库存日志
// @Schemes
// @Description 获取指定ID的库存日志信息
// @Tags 仓储模块,库存日志管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "库存日志ID"
// @Param _expand query string false "展开关联信息，支持: item,sku" example:"item,sku"
// @Success 200 {object} v1.Response{data=v1.WmsLogResponse}
// @Router /wms/logs/{id} [get]
func (h *WmsLogHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	log, err := h.wmsLogService.Get(ctx, uint(id), strings.Split(ctx.Query("_expand"), ","))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, log)
}

// List godoc
// @Summary 获取库存日志列表
// @Schemes
// @Description 分页获取库存日志列表
// @Tags 仓储模块,库存日志管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param itemId query int false "物料ID筛选" example:"1"
// @Param skuId query int false "规格ID筛选" example:"1"
// @Param type query int false "类型筛选 1-入库 2-出库 3-调拨 4-盘点 5-其它" example:"1"
// @Param relatedNo query string false "关联单号筛选" example:"IN202312010001"
// @Param _expand query string false "展开关联信息，支持: item,sku" example:"item,sku"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.WmsLogResponse}}
// @Router /wms/logs [get]
func (h *WmsLogHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	// 物料ID筛选
	if itemId := ctx.DefaultQuery("itemId", ""); itemId != "" {
		params.AddFilter("item_id", itemId)
	}

	// 规格ID筛选
	if skuId := ctx.DefaultQuery("skuId", ""); skuId != "" {
		params.AddFilter("sku_id", skuId)
	}

	// 类型筛选
	if logType := ctx.DefaultQuery("type", ""); logType != "" {
		params.AddFilter("type", logType)
	}

	// 关联单号筛选
	if relatedNo := ctx.DefaultQuery("relatedNo", ""); relatedNo != "" {
		params.AddFilter("related_no_like", relatedNo)
	}

	result, err := h.wmsLogService.List(ctx, &params, strings.Split(ctx.Query("_expand"), ","))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
