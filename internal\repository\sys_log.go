package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type SysLogRepository interface {
	Create(ctx context.Context, log *model.SysLog) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.SysLog, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.SysLog, int64, error)
}

func NewSysLogRepository(
	repository *Repository,
) SysLogRepository {
	return &sysLogRepository{
		Repository: repository,
	}
}

type sysLogRepository struct {
	*Repository
}

func (r *sysLogRepository) Create(ctx context.Context, log *model.SysLog) error {
	if err := r.DB(ctx).Create(log).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysLogRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.SysLog{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysLogRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.SysLog{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysLogRepository) Get(ctx context.Context, id uint) (*model.SysLog, error) {
	var log model.SysLog
	if err := r.DB(ctx).First(&log, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &log, nil
}

func (r *sysLogRepository) List(ctx context.Context, params *pagination.Params) ([]*model.SysLog, int64, error) {
	var records []*model.SysLog
	var total int64

	db := r.DB(ctx).Model(&model.SysLog{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.SysLog{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
