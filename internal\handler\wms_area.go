package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type WmsAreaHandler struct {
	*Handler
	wmsAreaService service.WmsAreaService
}

func NewWmsAreaHandler(
	handler *Handler,
	wmsAreaService service.WmsAreaService,
) *WmsAreaHandler {
	return &WmsAreaHandler{
		Handler:        handler,
		wmsAreaService: wmsAreaService,
	}
}

// Create godoc
// @Summary 创建仓库区域
// @Schemes
// @Description 创建新的仓库区域记录
// @Tags 仓储模块,仓库管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.WmsAreaCreateParams true "仓库区域信息"
// @Success 200 {object} v1.Response
// @Router /wms/areas [post]
func (h *WmsAreaHandler) Create(ctx *gin.Context) {
	var req v1.WmsAreaCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsAreaService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新仓库区域
// @Schemes
// @Description 更新指定ID的仓库区域信息
// @Tags 仓储模块,仓库管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "仓库区域ID"
// @Param request body v1.WmsAreaUpdateParams true "仓库区域信息"
// @Success 200 {object} v1.Response
// @Router /wms/areas/{id} [patch]
func (h *WmsAreaHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.WmsAreaUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsAreaService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除仓库区域
// @Schemes
// @Description 删除指定ID的仓库区域
// @Tags 仓储模块,仓库管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "仓库区域ID"
// @Success 200 {object} v1.Response
// @Router /wms/areas/{id} [delete]
func (h *WmsAreaHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsAreaService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除仓库区域
// @Schemes
// @Description 批量删除指定IDs的仓库区域
// @Tags 仓储模块,仓库管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "仓库区域IDs"
// @Success 200 {object} v1.Response
// @Router /wms/areas [delete]
func (h *WmsAreaHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsAreaService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取仓库区域
// @Schemes
// @Description 获取指定ID的仓库区域信息
// @Tags 仓储模块,仓库管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "仓库区域ID"
// @Success 200 {object} v1.Response{data=v1.WmsAreaResponse}
// @Router /wms/areas/{id} [get]
func (h *WmsAreaHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	area, err := h.wmsAreaService.Get(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, area)
}

// List godoc
// @Summary 获取仓库区域列表
// @Schemes
// @Description 分页获取仓库区域列表
// @Tags 仓储模块,仓库管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param status query bool false "状态筛选" example:"true"
// @Param type query int false "类型筛选" example:"1"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.WmsAreaResponse}}
// @Router /wms/areas [get]
func (h *WmsAreaHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	// 状态筛选
	if status := ctx.DefaultQuery("status", ""); status != "" {
		params.AddFilter("status", status)
	}

	// 名称筛选
	if name := ctx.DefaultQuery("name", ""); name != "" {
		params.AddFilter("name_like", name)
	}

	// 编号筛选
	if code := ctx.DefaultQuery("code", ""); code != "" {
		params.AddFilter("code_like", code)
	}

	// 类型筛选
	if areaType := ctx.DefaultQuery("type", ""); areaType != "" {
		params.AddFilter("type", areaType)
	}

	result, err := h.wmsAreaService.List(ctx, &params)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
