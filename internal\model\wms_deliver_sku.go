package model

import (
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// 出库单物料明细表，用于管理仓库的出库单物料明细信息
type WmsDeliverSku struct {
	gorm.Model
	DeliverId uint           `gorm:"default:0; index; comment:出库单ID"`
	ItemId    uint           `gorm:"default:0; index; comment:物料ID"`
	SkuId     uint           `gorm:"default:0; index; comment:规格ID"`
	Num       float64        `gorm:"type:numeric(10,2); default:0; comment:出库数量"`
	AreaNum   datatypes.JSON `gorm:"type:jsonb; comment:出库配置"`
	Pcs       float64        `gorm:"type:numeric(10,2); default:0; comment:包装规格"`
	Status    bool           `gorm:"default:false; index; comment:状态"`
	TenantId  uint           `gorm:"default:0; index; comment:租户ID" copier:"-"`
	CreatedBy string         `gorm:"size:64; comment:创建人" copier:"-"`
	UpdatedBy string         `gorm:"size:64; comment:操作人" copier:"-"`
}

func (m *WmsDeliverSku) TableName() string {
	return "wms_deliver_sku"
}
