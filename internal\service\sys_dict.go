package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/jinzhu/copier"
)

type SysDictService interface {
	Create(ctx context.Context, req *v1.SysDictCreateParams) error
	Update(ctx context.Context, id uint, req *v1.SysDictUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.SysDictResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewSysDictService(
	service *Service,
	sysDictRepository repository.SysDictRepository,
) SysDictService {
	return &sysDictService{
		Service:           service,
		sysDictRepository: sysDictRepository,
	}
}

type sysDictService struct {
	*Service
	sysDictRepository repository.SysDictRepository
}

func (s *sysDictService) Create(ctx context.Context, req *v1.SysDictCreateParams) error {
	dict := &model.SysDict{}
	if err := copier.Copy(dict, req); err != nil {
		return err
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysDictRepository.Create(ctx, dict); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysDictService) Update(ctx context.Context, id uint, req *v1.SysDictUpdateParams) error {
	dict, err := s.sysDictRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	if err := copier.Copy(dict, req); err != nil {
		return err
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysDictRepository.Update(ctx, dict); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysDictService) Delete(ctx context.Context, id uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysDictRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysDictService) BatchDelete(ctx context.Context, ids []uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysDictRepository.BatchDelete(ctx, ids); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysDictService) Get(ctx context.Context, id uint) (*v1.SysDictResponse, error) {
	dict, err := s.sysDictRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	response := &v1.SysDictResponse{}
	if err := copier.Copy(response, dict); err != nil {
		return nil, err
	}

	response.CreatedAt = dict.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = dict.UpdatedAt.Format(time.RFC3339)

	return response, nil
}

func (s *sysDictService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	dicts, total, err := s.sysDictRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	records := make([]*v1.SysDictResponse, 0, len(dicts))
	for _, dict := range dicts {
		response := &v1.SysDictResponse{}
		if err := copier.Copy(response, dict); err != nil {
			return nil, err
		}

		response.CreatedAt = dict.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = dict.UpdatedAt.Format(time.RFC3339)

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
