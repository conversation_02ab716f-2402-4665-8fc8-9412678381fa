package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/jinzhu/copier"
)

type WmsSkuService interface {
	Create(ctx context.Context, req *v1.WmsSkuCreateParams) error
	Update(ctx context.Context, id uint, req *v1.WmsSkuUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.WmsSkuResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewWmsSkuService(
	service *Service,
	wmsSkuRepository repository.WmsSkuRepository,
) WmsSkuService {
	return &wmsSkuService{
		Service:          service,
		wmsSkuRepository: wmsSkuRepository,
	}
}

type wmsSkuService struct {
	*Service
	wmsSkuRepository repository.WmsSkuRepository
}

// 物料规格相关方法实现
func (s *wmsSkuService) Create(ctx context.Context, req *v1.WmsSkuCreateParams) error {
	sku := &model.WmsSku{}
	if err := copier.Copy(sku, req); err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 租户ID
	sku.TenantId = user.TenantId

	// 创建人
	if user.Nickname != "" {
		sku.CreatedBy = user.Nickname
	} else {
		sku.CreatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsSkuRepository.Create(ctx, sku); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsSkuService) Update(ctx context.Context, id uint, req *v1.WmsSkuUpdateParams) error {
	sku, err := s.wmsSkuRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if sku.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	if err := copier.Copy(sku, req); err != nil {
		return err
	}

	// 更新人
	if user.Nickname != "" {
		sku.UpdatedBy = user.Nickname
	} else {
		sku.UpdatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsSkuRepository.Update(ctx, sku); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsSkuService) Delete(ctx context.Context, id uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 是否是该租户数据
	sku, err := s.wmsSkuRepository.Get(ctx, id)
	if err != nil {
		return err
	}
	if sku.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsSkuRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsSkuService) BatchDelete(ctx context.Context, ids []uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 获取目标记录
	skus, err := s.wmsSkuRepository.GetByIds(ctx, ids)
	if err != nil {
		return err
	}
	
	// 过滤非本租户数据
	var newIds []uint
	for _, sku := range skus {
		if sku.TenantId == user.TenantId {
			newIds = append(newIds, sku.ID)
		}
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsSkuRepository.BatchDelete(ctx, newIds); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsSkuService) Get(ctx context.Context, id uint) (*v1.WmsSkuResponse, error) {
	sku, err := s.wmsSkuRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if sku.TenantId != user.TenantId {
		return nil, v1.ErrForbidden
	}

	response := &v1.WmsSkuResponse{}
	if err := copier.Copy(response, sku); err != nil {
		return nil, err
	}

	response.CreatedAt = sku.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = sku.UpdatedAt.Format(time.RFC3339)

	return response, nil
}

func (s *wmsSkuService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	params.AddFilter("tenant_id", user.TenantId)

	skus, total, err := s.wmsSkuRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.WmsSkuResponse, 0, len(skus))
	for _, sku := range skus {
		response := &v1.WmsSkuResponse{}
		if err := copier.Copy(response, sku); err != nil {
			return nil, err
		}

		response.CreatedAt = sku.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = sku.UpdatedAt.Format(time.RFC3339)

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
