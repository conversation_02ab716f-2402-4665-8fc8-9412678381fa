package task

import (
	"context"
	"daisy-server/internal/repository"
)

type UserTask interface {
	CheckUser(ctx context.Context) error
}

func NewUserTask(
	task *Task,
	userRepo repository.SysUserRepository,
) UserTask {
	return &userTask{
		userRepo: userRepo,
		Task:     task,
	}
}

type userTask struct {
	userRepo repository.SysUserRepository
	*Task
}

func (t userTask) CheckUser(ctx context.Context) error {
	// do something
	return nil
}
