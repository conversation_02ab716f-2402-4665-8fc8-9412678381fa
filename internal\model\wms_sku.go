package model

import (
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// 物料规格表，用于管理物料的规格信息
type WmsSku struct {
	gorm.Model
	Name      string         `gorm:"size:255; not null; index; comment:名称"`
	Code      string         `gorm:"size:64; not null; index; comment:编号"`
	Unit      string         `gorm:"size:64; not null; comment:单位"`
	Min       float64        `gorm:"type:numeric(10,2); default:0; comment:最小库存"`
	Attrs     datatypes.JSON `gorm:"type:jsonb; comment:附加属性"`
	Summary   string         `gorm:"type:text; comment:备注"`
	Order     int            `gorm:"default:0; index; comment:排序"`
	Status    bool           `gorm:"default:false; index; comment:状态"`
	ItemId    uint           `gorm:"default:0; index; comment:物料ID"`
	TenantId  uint           `gorm:"default:0; index; comment:租户ID" copier:"-"`
	CreatedBy string         `gorm:"size:64; comment:创建人" copier:"-"`
	UpdatedBy string         `gorm:"size:64; comment:操作人" copier:"-"`
}

func (m *WmsSku) TableName() string {
	return "wms_sku"
}
