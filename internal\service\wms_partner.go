package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/jinzhu/copier"
)

type WmsPartnerService interface {
	Create(ctx context.Context, req *v1.WmsPartnerCreateParams) error
	Update(ctx context.Context, id uint, req *v1.WmsPartnerUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.WmsPartnerResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewWmsPartnerService(
	service *Service,
	wmsPartnerRepository repository.WmsPartnerRepository,
) WmsPartnerService {
	return &wmsPartnerService{
		Service:              service,
		wmsPartnerRepository: wmsPartnerRepository,
	}
}

type wmsPartnerService struct {
	*Service
	wmsPartnerRepository repository.WmsPartnerRepository
}

// 合作伙伴相关方法实现
func (s *wmsPartnerService) Create(ctx context.Context, req *v1.WmsPartnerCreateParams) error {
	partner := &model.WmsPartner{}
	if err := copier.Copy(partner, req); err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 租户ID
	partner.TenantId = user.TenantId

	// 创建人
	if user.Nickname != "" {
		partner.CreatedBy = user.Nickname
	} else {
		partner.CreatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsPartnerRepository.Create(ctx, partner); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsPartnerService) Update(ctx context.Context, id uint, req *v1.WmsPartnerUpdateParams) error {
	partner, err := s.wmsPartnerRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if partner.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	if err := copier.Copy(partner, req); err != nil {
		return err
	}

	// 更新人
	if user.Nickname != "" {
		partner.UpdatedBy = user.Nickname
	} else {
		partner.UpdatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsPartnerRepository.Update(ctx, partner); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsPartnerService) Delete(ctx context.Context, id uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 是否是该租户数据
	partner, err := s.wmsPartnerRepository.Get(ctx, id)
	if err != nil {
		return err
	}
	if partner.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsPartnerRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsPartnerService) BatchDelete(ctx context.Context, ids []uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 获取目标记录
	partners, err := s.wmsPartnerRepository.GetByIds(ctx, ids)
	if err != nil {
		return err
	}
	
	// 过滤非本租户数据
	var newIds []uint
	for _, partner := range partners {
		if partner.TenantId == user.TenantId {
			newIds = append(newIds, partner.ID)
		}
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsPartnerRepository.BatchDelete(ctx, newIds); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsPartnerService) Get(ctx context.Context, id uint) (*v1.WmsPartnerResponse, error) {
	partner, err := s.wmsPartnerRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if partner.TenantId != user.TenantId {
		return nil, v1.ErrForbidden
	}

	response := &v1.WmsPartnerResponse{}
	if err := copier.Copy(response, partner); err != nil {
		return nil, err
	}

	response.CreatedAt = partner.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = partner.UpdatedAt.Format(time.RFC3339)

	return response, nil
}

func (s *wmsPartnerService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	params.AddFilter("tenant_id", user.TenantId)

	partners, total, err := s.wmsPartnerRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.WmsPartnerResponse, 0, len(partners))
	for _, partner := range partners {
		response := &v1.WmsPartnerResponse{}
		if err := copier.Copy(response, partner); err != nil {
			return nil, err
		}

		response.CreatedAt = partner.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = partner.UpdatedAt.Format(time.RFC3339)

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
