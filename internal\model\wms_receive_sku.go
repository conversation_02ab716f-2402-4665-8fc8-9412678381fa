package model

import (
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// 入库单物料明细表，用于管理仓库的入库单物料明细信息
type WmsReceiveSku struct {
	gorm.Model
	ReceiveId uint           `gorm:"default:0; index; comment:入库单ID"`
	ItemId    uint           `gorm:"default:0; index; comment:物料ID"`
	SkuId     uint           `gorm:"default:0; index; comment:规格ID"`
	BatchNo   string         `gorm:"size:64; not null; index; comment:批次号"`
	Num       float64        `gorm:"type:numeric(10,2); default:0; comment:入库数量"`
	AreaNum   datatypes.JSON `gorm:"type:jsonb; comment:入库配置"`
	Status    bool           `gorm:"default:false; index; comment:状态"`
	TenantId  uint           `gorm:"default:0; index; comment:租户ID" copier:"-"`
	CreatedBy string         `gorm:"size:64; comment:创建人" copier:"-"`
	UpdatedBy string         `gorm:"size:64; comment:操作人" copier:"-"`
}

func (m *WmsReceiveSku) TableName() string {
	return "wms_receive_sku"
}
