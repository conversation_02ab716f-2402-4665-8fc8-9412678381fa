package v1

import (
	"time"
)

type SysTenantCreateParams struct {
	Name      string    `json:"name" binding:"required,max=64" example:"租户名称"`
	Code      string    `json:"code" binding:"required,max=64" example:"tenant-code"`
	Summary   string    `json:"summary" example:"租户描述"`
	ExpiredAt time.Time `json:"expiredAt" example:"2024-12-31T23:59:59Z"`
	Status    bool      `json:"status" example:"true"`
}

type SysTenantUpdateParams struct {
	SysTenantCreateParams
}

type SysTenantResponse struct {
	ID        uint   `json:"id"`
	Name      string `json:"name"`
	Code      string `json:"code"`
	Summary   string `json:"summary"`
	ExpiredAt string `json:"expiredAt"`
	Status    bool   `json:"status"`
	CreatedAt string `json:"createdAt"`
	UpdatedAt string `json:"updatedAt"`
}
