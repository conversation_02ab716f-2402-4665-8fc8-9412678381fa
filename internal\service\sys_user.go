package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"slices"
	"time"

	"github.com/jinzhu/copier"
	"golang.org/x/crypto/bcrypt"
)

type SysUserService interface {
	Create(ctx context.Context, req *v1.SysUserCreateParams) error
	Update(ctx context.Context, id uint, req *v1.SysUserUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint, expand []string) (*v1.SysUserResponse, error)
	List(ctx context.Context, params *pagination.Params, expand []string) (*pagination.Result, error)
}

func NewSysUserService(
	service *Service,
	sysUserRepository repository.SysUserRepository,
	sysTenantRepository repository.SysTenantRepository,
) SysUserService {
	return &sysUserService{
		Service:             service,
		sysUserRepository:   sysUserRepository,
		sysTenantRepository: sysTenantRepository,
	}
}

type sysUserService struct {
	*Service
	sysUserRepository   repository.SysUserRepository
	sysTenantRepository repository.SysTenantRepository
}

// 用户相关方法实现
func (s *sysUserService) Create(ctx context.Context, req *v1.SysUserCreateParams) error {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}

	// 生成用户ID
	userId, err := s.sid.GenString()
	if err != nil {
		return err
	}

	user := &model.SysUser{}
	if err := copier.Copy(user, req); err != nil {
		return err
	}

	user.UserId = userId
	user.Password = string(hashedPassword)

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysUserRepository.Create(ctx, user); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysUserService) Update(ctx context.Context, id uint, req *v1.SysUserUpdateParams) error {
	user, err := s.sysUserRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	if err := copier.Copy(user, req); err != nil {
		return err
	}

	// 修改密码
	if req.Password != "" {
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
		if err != nil {
			return err
		}
		user.Password = string(hashedPassword)
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysUserRepository.Update(ctx, user); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysUserService) Delete(ctx context.Context, id uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysUserRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysUserService) BatchDelete(ctx context.Context, ids []uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysUserRepository.BatchDelete(ctx, ids); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysUserService) Get(ctx context.Context, id uint, expand []string) (*v1.SysUserResponse, error) {
	user, err := s.sysUserRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	response := &v1.SysUserResponse{}
	if err := copier.Copy(response, user); err != nil {
		return nil, err
	}

	response.CreatedAt = user.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = user.UpdatedAt.Format(time.RFC3339)

	// 展开租户信息
	if slices.Contains(expand, "tenant") && user.TenantId > 0 {
		tenant, err := s.sysTenantRepository.Get(ctx, user.TenantId)
		if err == nil {
			tenantResponse := &v1.SysTenantResponse{}
			if err := copier.Copy(tenantResponse, tenant); err == nil {
				tenantResponse.CreatedAt = tenant.CreatedAt.Format(time.RFC3339)
				tenantResponse.UpdatedAt = tenant.UpdatedAt.Format(time.RFC3339)
				tenantResponse.ExpiredAt = tenant.ExpiredAt.Format(time.RFC3339)
				response.Tenant = tenantResponse
			}
		}
	}

	return response, nil
}

func (s *sysUserService) List(ctx context.Context, params *pagination.Params, expand []string) (*pagination.Result, error) {
	users, total, err := s.sysUserRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.SysUserResponse, 0, len(users))
	for _, user := range users {
		response := &v1.SysUserResponse{}
		if err := copier.Copy(response, user); err != nil {
			return nil, err
		}

		response.CreatedAt = user.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = user.UpdatedAt.Format(time.RFC3339)

		// 展开租户信息
		if slices.Contains(expand, "tenant") && user.TenantId > 0 {
			tenant, err := s.sysTenantRepository.Get(ctx, user.TenantId)
			if err == nil {
				tenantResponse := &v1.SysTenantResponse{}
				if err := copier.Copy(tenantResponse, tenant); err == nil {
					tenantResponse.CreatedAt = tenant.CreatedAt.Format(time.RFC3339)
					tenantResponse.UpdatedAt = tenant.UpdatedAt.Format(time.RFC3339)
					tenantResponse.ExpiredAt = tenant.ExpiredAt.Format(time.RFC3339)
					response.Tenant = tenantResponse
				}
			}
		}

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
