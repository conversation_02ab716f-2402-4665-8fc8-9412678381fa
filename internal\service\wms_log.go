package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"slices"
	"time"

	"github.com/jinzhu/copier"
)

type WmsLogService interface {
	Create(ctx context.Context, req *v1.WmsLogCreateParams) error
	Update(ctx context.Context, id uint, req *v1.WmsLogUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint, expand []string) (*v1.WmsLogResponse, error)
	List(ctx context.Context, params *pagination.Params, expand []string) (*pagination.Result, error)
}

func NewWmsLogService(
	service *Service,
	wmsLogRepository repository.WmsLogRepository,
	wmsItemRepository repository.WmsItemRepository,
	wmsSkuRepository repository.WmsSkuRepository,
) WmsLogService {
	return &wmsLogService{
		Service:           service,
		wmsLogRepository:  wmsLogRepository,
		wmsItemRepository: wmsItemRepository,
		wmsSkuRepository:  wmsSkuRepository,
	}
}

type wmsLogService struct {
	*Service
	wmsLogRepository  repository.WmsLogRepository
	wmsItemRepository repository.WmsItemRepository
	wmsSkuRepository  repository.WmsSkuRepository
}

// 库存日志相关方法实现
func (s *wmsLogService) Create(ctx context.Context, req *v1.WmsLogCreateParams) error {
	log := &model.WmsLog{}
	if err := copier.Copy(log, req); err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 租户ID
	log.TenantId = user.TenantId

	// 创建人
	if user.Nickname != "" {
		log.CreatedBy = user.Nickname
	} else {
		log.CreatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsLogRepository.Create(ctx, log); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsLogService) Update(ctx context.Context, id uint, req *v1.WmsLogUpdateParams) error {
	log, err := s.wmsLogRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if log.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	if err := copier.Copy(log, req); err != nil {
		return err
	}

	// 更新人
	if user.Nickname != "" {
		log.UpdatedBy = user.Nickname
	} else {
		log.UpdatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsLogRepository.Update(ctx, log); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsLogService) Delete(ctx context.Context, id uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 是否是该租户数据
	log, err := s.wmsLogRepository.Get(ctx, id)
	if err != nil {
		return err
	}
	if log.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsLogRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsLogService) BatchDelete(ctx context.Context, ids []uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 获取目标记录
	logs, err := s.wmsLogRepository.GetByIds(ctx, ids)
	if err != nil {
		return err
	}

	// 过滤非本租户数据
	var newIds []uint
	for _, log := range logs {
		if log.TenantId == user.TenantId {
			newIds = append(newIds, log.ID)
		}
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsLogRepository.BatchDelete(ctx, newIds); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsLogService) Get(ctx context.Context, id uint, expand []string) (*v1.WmsLogResponse, error) {
	log, err := s.wmsLogRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if log.TenantId != user.TenantId {
		return nil, v1.ErrForbidden
	}

	response := &v1.WmsLogResponse{}
	if err := copier.Copy(response, log); err != nil {
		return nil, err
	}

	response.CreatedAt = log.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = log.UpdatedAt.Format(time.RFC3339)

	// 展开物料信息
	if slices.Contains(expand, "item") && log.ItemId > 0 {
		item, err := s.wmsItemRepository.Get(ctx, log.ItemId)
		if err == nil {
			itemResponse := &v1.WmsItemResponse{}
			if err := copier.Copy(itemResponse, item); err == nil {
				itemResponse.CreatedAt = item.CreatedAt.Format(time.RFC3339)
				itemResponse.UpdatedAt = item.UpdatedAt.Format(time.RFC3339)
				response.Item = itemResponse
			}
		}
	}

	// 展开SKU信息
	if slices.Contains(expand, "sku") && log.SkuId > 0 {
		sku, err := s.wmsSkuRepository.Get(ctx, log.SkuId)
		if err == nil {
			skuResponse := &v1.WmsSkuResponse{}
			if err := copier.Copy(skuResponse, sku); err == nil {
				skuResponse.CreatedAt = sku.CreatedAt.Format(time.RFC3339)
				skuResponse.UpdatedAt = sku.UpdatedAt.Format(time.RFC3339)
				response.Sku = skuResponse
			}
		}
	}

	return response, nil
}

func (s *wmsLogService) List(ctx context.Context, params *pagination.Params, expand []string) (*pagination.Result, error) {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	params.AddFilter("tenant_id", user.TenantId)

	logs, total, err := s.wmsLogRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.WmsLogResponse, 0, len(logs))
	for _, log := range logs {
		response := &v1.WmsLogResponse{}
		if err := copier.Copy(response, log); err != nil {
			return nil, err
		}

		response.CreatedAt = log.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = log.UpdatedAt.Format(time.RFC3339)

		// 展开物料信息
		if slices.Contains(expand, "item") && log.ItemId > 0 {
			item, err := s.wmsItemRepository.Get(ctx, log.ItemId)
			if err == nil {
				itemResponse := &v1.WmsItemResponse{}
				if err := copier.Copy(itemResponse, item); err == nil {
					itemResponse.CreatedAt = item.CreatedAt.Format(time.RFC3339)
					itemResponse.UpdatedAt = item.UpdatedAt.Format(time.RFC3339)
					response.Item = itemResponse
				}
			}
		}

		// 展开SKU信息
		if slices.Contains(expand, "sku") && log.SkuId > 0 {
			sku, err := s.wmsSkuRepository.Get(ctx, log.SkuId)
			if err == nil {
				skuResponse := &v1.WmsSkuResponse{}
				if err := copier.Copy(skuResponse, sku); err == nil {
					skuResponse.CreatedAt = sku.CreatedAt.Format(time.RFC3339)
					skuResponse.UpdatedAt = sku.UpdatedAt.Format(time.RFC3339)
					response.Sku = skuResponse
				}
			}
		}

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
