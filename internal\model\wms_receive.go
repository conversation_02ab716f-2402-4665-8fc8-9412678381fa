package model

import "gorm.io/gorm"

// 入库单表，用于管理仓库的入库单信息
type WmsReceive struct {
	gorm.Model
	Code      string `gorm:"size:64; not null; index; comment:编号"`
	Type      string `gorm:"size:64; not null; comment:类型"`
	PartnerId uint   `gorm:"default:0; index; comment:供应商ID"`
	RelatedNo string `gorm:"size:64; not null; index; comment:关联编号"`
	Summary   string `gorm:"type:text; comment:备注"`
	Status    bool   `gorm:"default:false; index; comment:状态"`
	TenantId  uint   `gorm:"default:0; index; comment:租户ID" copier:"-"`
	CreatedBy string `gorm:"size:64; comment:创建人" copier:"-"`
	UpdatedBy string `gorm:"size:64; comment:操作人" copier:"-"`
}

func (m *WmsReceive) TableName() string {
	return "wms_receive"
}
