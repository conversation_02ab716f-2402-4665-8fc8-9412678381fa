package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type CmsMetaRepository interface {
	Create(ctx context.Context, meta *model.CmsMeta) error
	Update(ctx context.Context, meta *model.CmsMeta) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.CmsMeta, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.CmsMeta, int64, error)
}

func NewCmsMetaRepository(
	repository *Repository,
) CmsMetaRepository {
	return &cmsMetaRepository{
		Repository: repository,
	}
}

type cmsMetaRepository struct {
	*Repository
}

func (r *cmsMetaRepository) Create(ctx context.Context, meta *model.CmsMeta) error {
	if err := r.DB(ctx).Create(meta).Error; err != nil {
		return err
	}
	return nil
}

func (r *cmsMetaRepository) Update(ctx context.Context, meta *model.CmsMeta) error {
	if err := r.DB(ctx).Save(meta).Error; err != nil {
		return err
	}
	return nil
}

func (r *cmsMetaRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.CmsMeta{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *cmsMetaRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.CmsMeta{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *cmsMetaRepository) Get(ctx context.Context, id uint) (*model.CmsMeta, error) {
	var meta model.CmsMeta
	if err := r.DB(ctx).First(&meta, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &meta, nil
}

func (r *cmsMetaRepository) List(ctx context.Context, params *pagination.Params) ([]*model.CmsMeta, int64, error) {
	var records []*model.CmsMeta
	var total int64

	db := r.DB(ctx).Model(&model.CmsMeta{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.CmsMeta{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
