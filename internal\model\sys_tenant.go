package model

import (
	"time"

	"gorm.io/gorm"
)

// 租户表，用于管理多租户信息
type SysTenant struct {
	gorm.Model
	Name      string    `gorm:"size:64; not null; unique; comment:名称"`
	Code      string    `gorm:"size:64; not null; unique; comment:编码"`
	Summary   string    `gorm:"type:text; comment:描述"`
	ExpiredAt time.Time `gorm:"index; comment:过期时间"`
	Status    bool      `gorm:"default:false; index; comment:状态"`
}

func (m *SysTenant) TableName() string {
	return "sys_tenant"
}
