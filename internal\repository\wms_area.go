package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type WmsAreaRepository interface {
	Create(ctx context.Context, area *model.WmsArea) error
	Update(ctx context.Context, area *model.WmsArea) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.WmsArea, error)
	GetByIds(ctx context.Context, ids []uint) ([]*model.WmsArea, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.WmsArea, int64, error)
}

func NewWmsAreaRepository(
	repository *Repository,
) WmsAreaRepository {
	return &wmsAreaRepository{
		Repository: repository,
	}
}

type wmsAreaRepository struct {
	*Repository
}

func (r *wmsAreaRepository) Create(ctx context.Context, area *model.WmsArea) error {
	if err := r.DB(ctx).Create(area).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsAreaRepository) Update(ctx context.Context, area *model.WmsArea) error {
	if err := r.DB(ctx).Save(area).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsAreaRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.WmsArea{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsAreaRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.WmsArea{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsAreaRepository) Get(ctx context.Context, id uint) (*model.WmsArea, error) {
	var area model.WmsArea
	if err := r.DB(ctx).First(&area, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &area, nil
}

func (r *wmsAreaRepository) GetByIds(ctx context.Context, ids []uint) ([]*model.WmsArea, error) {
	var areas []*model.WmsArea
	if len(ids) == 0 {
		return areas, nil
	}

	if err := r.DB(ctx).Where("id IN ?", ids).Find(&areas).Error; err != nil {
		return nil, err
	}
	return areas, nil
}

func (r *wmsAreaRepository) List(ctx context.Context, params *pagination.Params) ([]*model.WmsArea, int64, error) {
	var records []*model.WmsArea
	var total int64

	db := r.DB(ctx).Model(&model.WmsArea{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.WmsArea{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
