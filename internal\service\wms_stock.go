package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"slices"
	"time"

	"github.com/jinzhu/copier"
)

type WmsStockService interface {
	Create(ctx context.Context, req *v1.WmsStockCreateParams) error
	Update(ctx context.Context, id uint, req *v1.WmsStockUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint, expand []string) (*v1.WmsStockResponse, error)
	List(ctx context.Context, params *pagination.Params, expand []string) (*pagination.Result, error)
}

func NewWmsStockService(
	service *Service,
	wmsStockRepository repository.WmsStockRepository,
	wmsSkuRepository repository.WmsSkuRepository,
	wmsItemRepository repository.WmsItemRepository,
) WmsStockService {
	return &wmsStockService{
		Service:            service,
		wmsStockRepository: wmsStockRepository,
		wmsSkuRepository:   wmsSkuRepository,
		wmsItemRepository:  wmsItemRepository,
	}
}

type wmsStockService struct {
	*Service
	wmsStockRepository repository.WmsStockRepository
	wmsSkuRepository   repository.WmsSkuRepository
	wmsItemRepository  repository.WmsItemRepository
}

// 库存相关方法实现
func (s *wmsStockService) Create(ctx context.Context, req *v1.WmsStockCreateParams) error {
	stock := &model.WmsStock{}
	if err := copier.Copy(stock, req); err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 租户ID
	stock.TenantId = user.TenantId

	// 创建人
	if user.Nickname != "" {
		stock.CreatedBy = user.Nickname
	} else {
		stock.CreatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsStockRepository.Create(ctx, stock); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsStockService) Update(ctx context.Context, id uint, req *v1.WmsStockUpdateParams) error {
	stock, err := s.wmsStockRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if stock.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	if err := copier.Copy(stock, req); err != nil {
		return err
	}

	// 更新人
	if user.Nickname != "" {
		stock.UpdatedBy = user.Nickname
	} else {
		stock.UpdatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsStockRepository.Update(ctx, stock); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsStockService) Delete(ctx context.Context, id uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 是否是该租户数据
	stock, err := s.wmsStockRepository.Get(ctx, id)
	if err != nil {
		return err
	}
	if stock.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsStockRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsStockService) BatchDelete(ctx context.Context, ids []uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 获取目标记录
	stocks, err := s.wmsStockRepository.GetByIds(ctx, ids)
	if err != nil {
		return err
	}

	// 过滤非本租户数据
	var newIds []uint
	for _, stock := range stocks {
		if stock.TenantId == user.TenantId {
			newIds = append(newIds, stock.ID)
		}
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsStockRepository.BatchDelete(ctx, newIds); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsStockService) Get(ctx context.Context, id uint, expand []string) (*v1.WmsStockResponse, error) {
	stock, err := s.wmsStockRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if stock.TenantId != user.TenantId {
		return nil, v1.ErrForbidden
	}

	response := &v1.WmsStockResponse{}
	if err := copier.Copy(response, stock); err != nil {
		return nil, err
	}

	response.CreatedAt = stock.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = stock.UpdatedAt.Format(time.RFC3339)

	// 展开物料信息
	if slices.Contains(expand, "item") && stock.ItemId > 0 {
		item, err := s.wmsItemRepository.Get(ctx, stock.ItemId)
		if err == nil {
			itemResponse := &v1.WmsItemResponse{}
			if err := copier.Copy(itemResponse, item); err == nil {
				itemResponse.CreatedAt = item.CreatedAt.Format(time.RFC3339)
				itemResponse.UpdatedAt = item.UpdatedAt.Format(time.RFC3339)
				response.Item = itemResponse
			}
		}
	}

	// 展开SKU信息
	if slices.Contains(expand, "sku") && stock.SkuId > 0 {
		sku, err := s.wmsSkuRepository.Get(ctx, stock.SkuId)
		if err == nil {
			skuResponse := &v1.WmsSkuResponse{}
			if err := copier.Copy(skuResponse, sku); err == nil {
				skuResponse.CreatedAt = sku.CreatedAt.Format(time.RFC3339)
				skuResponse.UpdatedAt = sku.UpdatedAt.Format(time.RFC3339)
				response.Sku = skuResponse
			}
		}
	}

	return response, nil
}

func (s *wmsStockService) List(ctx context.Context, params *pagination.Params, expand []string) (*pagination.Result, error) {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	params.AddFilter("tenant_id", user.TenantId)

	stocks, total, err := s.wmsStockRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.WmsStockResponse, 0, len(stocks))
	for _, stock := range stocks {
		response := &v1.WmsStockResponse{}
		if err := copier.Copy(response, stock); err != nil {
			return nil, err
		}

		response.CreatedAt = stock.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = stock.UpdatedAt.Format(time.RFC3339)

		// 展开物料信息
		if slices.Contains(expand, "item") && stock.ItemId > 0 {
			item, err := s.wmsItemRepository.Get(ctx, stock.ItemId)
			if err == nil {
				itemResponse := &v1.WmsItemResponse{}
				if err := copier.Copy(itemResponse, item); err == nil {
					itemResponse.CreatedAt = item.CreatedAt.Format(time.RFC3339)
					itemResponse.UpdatedAt = item.UpdatedAt.Format(time.RFC3339)
					response.Item = itemResponse
				}
			}
		}

		// 展开SKU信息
		if slices.Contains(expand, "sku") && stock.SkuId > 0 {
			sku, err := s.wmsSkuRepository.Get(ctx, stock.SkuId)
			if err == nil {
				skuResponse := &v1.WmsSkuResponse{}
				if err := copier.Copy(skuResponse, sku); err == nil {
					skuResponse.CreatedAt = sku.CreatedAt.Format(time.RFC3339)
					skuResponse.UpdatedAt = sku.UpdatedAt.Format(time.RFC3339)
					response.Sku = skuResponse
				}
			}
		}

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
