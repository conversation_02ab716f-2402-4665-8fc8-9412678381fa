package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/jinzhu/copier"
)

type SysLogService interface {
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.SysLogResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewSysLogService(
	service *Service,
	sysLogRepository repository.SysLogRepository,
) SysLogService {
	return &sysLogService{
		Service:          service,
		sysLogRepository: sysLogRepository,
	}
}

type sysLogService struct {
	*Service
	sysLogRepository repository.SysLogRepository
}

func (s *sysLogService) Delete(ctx context.Context, id uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysLogRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysLogService) BatchDelete(ctx context.Context, ids []uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysLogRepository.BatchDelete(ctx, ids); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysLogService) Get(ctx context.Context, id uint) (*v1.SysLogResponse, error) {
	log, err := s.sysLogRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	response := &v1.SysLogResponse{}
	if err := copier.Copy(response, log); err != nil {
		return nil, err
	}

	response.CreatedAt = log.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = log.UpdatedAt.Format(time.RFC3339)

	return response, nil
}

func (s *sysLogService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	logs, total, err := s.sysLogRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.SysLogResponse, 0, len(logs))
	for _, log := range logs {
		response := &v1.SysLogResponse{}
		if err := copier.Copy(response, log); err != nil {
			return nil, err
		}

		response.CreatedAt = log.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = log.UpdatedAt.Format(time.RFC3339)

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
