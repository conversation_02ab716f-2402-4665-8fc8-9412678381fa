package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type WmsUnitRepository interface {
	Create(ctx context.Context, unit *model.WmsUnit) error
	Update(ctx context.Context, unit *model.WmsUnit) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.WmsUnit, error)
	GetByIds(ctx context.Context, ids []uint) ([]*model.WmsUnit, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.WmsUnit, int64, error)
}

func NewWmsUnitRepository(
	repository *Repository,
) WmsUnitRepository {
	return &wmsUnitRepository{
		Repository: repository,
	}
}

type wmsUnitRepository struct {
	*Repository
}

func (r *wmsUnitRepository) Create(ctx context.Context, unit *model.WmsUnit) error {
	if err := r.DB(ctx).Create(unit).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsUnitRepository) Update(ctx context.Context, unit *model.WmsUnit) error {
	if err := r.DB(ctx).Save(unit).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsUnitRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.WmsUnit{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsUnitRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.WmsUnit{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsUnitRepository) Get(ctx context.Context, id uint) (*model.WmsUnit, error) {
	var unit model.WmsUnit
	if err := r.DB(ctx).First(&unit, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &unit, nil
}

func (r *wmsUnitRepository) GetByIds(ctx context.Context, ids []uint) ([]*model.WmsUnit, error) {
	var units []*model.WmsUnit
	if len(ids) == 0 {
		return units, nil
	}
	
	if err := r.DB(ctx).Where("id IN ?", ids).Find(&units).Error; err != nil {
		return nil, err
	}
	return units, nil
}

func (r *wmsUnitRepository) List(ctx context.Context, params *pagination.Params) ([]*model.WmsUnit, int64, error) {
	var records []*model.WmsUnit
	var total int64

	db := r.DB(ctx).Model(&model.WmsUnit{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.WmsUnit{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
