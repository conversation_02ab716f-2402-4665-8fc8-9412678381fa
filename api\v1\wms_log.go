package v1

type WmsLogCreateParams struct {
	ItemId    uint    `json:"itemId" binding:"required" example:"1"`
	SkuId     uint    `json:"skuId" binding:"required" example:"1"`
	Type      uint    `json:"type" binding:"required" example:"1"`
	RelatedNo string  `json:"relatedNo" binding:"required,max=64" example:"IN202312010001"`
	Num       float64 `json:"num" binding:"required" example:"100"`
	Remain    float64 `json:"remain" binding:"required" example:"100"`
	Summary   string  `json:"summary" example:"入库操作"`
	TenantId  uint    `json:"tenantId" example:"1"`
	CreatedBy string  `json:"createdBy" example:"管理员"`
	UpdatedBy string  `json:"updatedBy" example:"管理员"`
}

type WmsLogUpdateParams struct {
	WmsLogCreateParams
}

type WmsLogResponse struct {
	ID        uint             `json:"id"`
	ItemId    uint             `json:"itemId"`
	SkuId     uint             `json:"skuId"`
	Type      uint             `json:"type"`
	RelatedNo string           `json:"relatedNo"`
	Num       float64          `json:"num"`
	Remain    float64          `json:"remain"`
	Summary   string           `json:"summary"`
	TenantId  uint             `json:"tenantId"`
	CreatedBy string           `json:"createdBy"`
	UpdatedBy string           `json:"updatedBy"`
	CreatedAt string           `json:"createdAt"`
	UpdatedAt string           `json:"updatedAt"`
	Item      *WmsItemResponse `json:"item,omitempty"` // 物料信息
	Sku       *WmsSkuResponse  `json:"sku,omitempty"`  // SKU信息
}
