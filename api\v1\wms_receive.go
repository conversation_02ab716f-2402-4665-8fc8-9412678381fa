package v1

type WmsReceiveCreateParams struct {
	Code      string `json:"code" binding:"required,max=64" example:"REC001"`
	Type      string `json:"type" binding:"required,max=64" example:"采购入库"`
	PartnerId uint   `json:"partnerId" example:"1"`
	RelatedNo string `json:"relatedNo" binding:"required,max=64" example:"PO001"`
	Summary   string `json:"summary" example:"采购入库单"`
	Status    bool   `json:"status" example:"true"`
	TenantId  uint   `json:"tenantId" example:"1"`
	CreatedBy string `json:"createdBy" example:"管理员"`
	UpdatedBy string `json:"updatedBy" example:"管理员"`
}

type WmsReceiveUpdateParams struct {
	WmsReceiveCreateParams
}

type WmsReceiveResponse struct {
	ID        uint   `json:"id"`
	Code      string `json:"code"`
	Type      string `json:"type"`
	PartnerId uint   `json:"partnerId"`
	RelatedNo string `json:"relatedNo"`
	Summary   string `json:"summary"`
	Status    bool   `json:"status"`
	TenantId  uint   `json:"tenantId"`
	CreatedBy string `json:"createdBy"`
	UpdatedBy string `json:"updatedBy"`
	CreatedAt string `json:"createdAt"`
	UpdatedAt string `json:"updatedAt"`
}
