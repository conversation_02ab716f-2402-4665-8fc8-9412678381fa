package model

import "gorm.io/gorm"

// 物料包装表，用于管理物料的包装信息
type WmsPack struct {
	gorm.Model
	Code      string  `gorm:"size:64; not null; index; comment:编号"`
	DeliverId uint    `gorm:"default:0; index; comment:出库单ID"`
	ItemId    uint    `gorm:"default:0; index; comment:物料ID"`
	SkuId     uint    `gorm:"default:0; index; comment:规格ID"`
	Num       float64 `gorm:"type:numeric(10,2); default:0; comment:包装数量"`
	TenantId  uint    `gorm:"default:0; index; comment:租户ID" copier:"-"`
	CreatedBy string  `gorm:"size:64; comment:创建人" copier:"-"`
	UpdatedBy string  `gorm:"size:64; comment:操作人" copier:"-"`
}

func (m *WmsPack) TableName() string {
	return "wms_pack"
}
