package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type SysDictHandler struct {
	*Handler
	sysDictService service.SysDictService
}

func NewSysDictHandler(
	handler *Handler,
	sysDictService service.SysDictService,
) *SysDictHandler {
	return &SysDictHandler{
		Handler:        handler,
		sysDictService: sysDictService,
	}
}

// Create godoc
// @Summary 创建字典
// @Schemes
// @Description 创建新的字典记录
// @Tags 系统模块,字典管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.SysDictCreateParams true "字典信息"
// @Success 200 {object} v1.Response
// @Router /system/dicts [post]
func (h *SysDictHandler) Create(ctx *gin.Context) {
	var req v1.SysDictCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysDictService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新字典
// @Schemes
// @Description 更新指定ID的字典信息
// @Tags 系统模块,字典管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "字典ID"
// @Param request body v1.SysDictUpdateParams true "字典信息"
// @Success 200 {object} v1.Response
// @Router /system/dicts/{id} [patch]
func (h *SysDictHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.SysDictUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysDictService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除字典
// @Schemes
// @Description 删除指定ID的字典
// @Tags 系统模块,字典管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "字典ID"
// @Success 200 {object} v1.Response
// @Router /system/dicts/{id} [delete]
func (h *SysDictHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysDictService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除字典
// @Schemes
// @Description 批量删除指定IDs的字典
// @Tags 系统模块,字典管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "字典IDs"
// @Success 200 {object} v1.Response
// @Router /system/dicts [delete]
func (h *SysDictHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysDictService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取字典
// @Schemes
// @Description 获取指定ID的字典信息
// @Tags 系统模块,字典管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "字典ID"
// @Success 200 {object} v1.Response{data=v1.SysDictResponse}
// @Router /system/dicts/{id} [get]
func (h *SysDictHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}
	dict, err := h.sysDictService.Get(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}
	v1.HandleSuccess(ctx, dict)
}

// List godoc
// @Summary 获取字典列表
// @Schemes
// @Description 分页获取字典列表
// @Tags 系统模块,字典管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param status query bool false "状态筛选" example:"true"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.SysDictResponse}}
// @Router /system/dicts [get]
func (h *SysDictHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}
	result, err := h.sysDictService.List(ctx, &params)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}
	v1.HandleSuccess(ctx, result)
}
