services:
  postgres:
    image: postgres:17-alpine
    container_name: postgres
    ports:
      - 5432:5432
    environment:
      - POSTGRES_DB=postgres
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=smilE4475
      - TZ=Asia/Shanghai
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app
    restart: on-failure
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "postgres"]
      interval: 10s
      timeout: 10s
      retries: 3

  redis:
    image: valkey/valkey:8-alpine
    container_name: redis
    command: redis-server --requirepass smilE4475 --maxmemory 128mb --maxmemory-policy allkeys-lru
    ports:
      - 6379:6379
    volumes:
      - redis_data:/data
    networks:
      - app
    restart: on-failure
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "smilE4475", "ping"]
      interval: 5s
      timeout: 3s
      retries: 3
      start_period: 10s

  minio:
    image: minio/minio:RELEASE.2025-04-22T22-12-26Z
    container_name: minio
    command: server --console-address ":9001" /data
    ports:
      - 9000:9000
      - 9001:9001
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=smilE4475
      - TZ=Asia/Shanghai
    volumes:
      - minio_data:/data
    networks:
      - app
    restart: on-failure
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local

networks:
  app:
    driver: bridge